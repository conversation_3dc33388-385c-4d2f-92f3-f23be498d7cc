#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 0001: Contextual Expander for Personal Coding Principles
    "0001-a-my_coding_principles": {
        "title": "Contextual Expander and Explosive Decomposer",
        "interpretation": "Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:",
        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
        # "context": {
        #     "core_principles": {
        #         "non_negotiable_ideals": [
        #             "Clarity: Code communicates intent without explanatory prose.",
        #             "Simplicity: Small, elegant solutions beat complex, sprawling ones.",
        #             "Integrity: Never degrade existing behaviour, structure, or tests.",
        #             "Maintainability: Optimise for future comprehension and evolution.",
        #             "High-Leverage Impact: Prefer changes that yield maximum benefit with minimal disruption.",
        #             "Observability & Guardrails: Automation must surface every regression immediately.",
        #             "Reversibility: Design changes so they can be rolled back cleanly."
        #         ]
        #     },
        # },
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # generator_range=(1, 1000),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
