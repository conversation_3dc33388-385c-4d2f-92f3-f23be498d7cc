#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3900: AI Frontend Engineering Co-Pilot - Application Modification Orchestrator
    "3900-a-app_modification_orchestrator": {
        "title": "AppMod-A: Directive Interpreter & Scoper",
        "interpretation": "Your goal is not to **guess** the user's intent, but to **precisely analyze** the user's directive and existing application structure to define the core modification task and identify an initial set of affected files or components. Execute as:",
        "transformation": "`{role=directive_analyzer_and_scoper; input=[user_directive:str, existing_file_paths:list_str]; process=[tokenize_user_directive(), identify_key_actions_and_entities(), correlate_entities_with_file_paths(), infer_primary_modification_goal(), list_initially_impacted_files()]; constraints=[focus_on_literal_interpretation_of_directive(), avoid_premature_solution_design(), do_not_infer_unspecified_functionality()]; requirements=[output_must_clearly_state_main_task(), impacted_files_list_must_be_based_on_directive_keywords_and_existing_files()]; output={core_task_definition:str, initial_affected_files:list_str}}`"
    },
    "3900-b-app_modification_orchestrator": {
        "title": "AppMod-B: Specification Developer",
        "interpretation": "Your goal is not to **write code directly**, but to **construct a comprehensive specification** for the required changes, detailing UI/UX considerations, functional requirements, aesthetic goals, and precise modifications for each targeted file based on the interpreted directive. Execute as:",
        "transformation": "`{role=change_specification_architect; input=[core_task_definition:str, initial_affected_files:list_str, existing_file_contents:map_str_str]; process=[elaborate_functional_requirements_from_task(), define_ui_ux_impact_and_design_choices(), specify_aesthetic_adjustments(), outline_code_logic_changes_per_file(), identify_necessary_new_assets_or_dependencies_if_any(), consider_accessibility_and_responsiveness_needs()]; constraints=[specification_must_be_actionable_for_code_generation(), adhere_to_ux_best_practices(), changes_must_align_with_overall_app_purpose()]; requirements=[detailed_breakdown_of_changes_per_file(), clear_acceptance_criteria_for_each_change(), visual_design_notes_if_applicable()]; output={detailed_specification:json_object}}`"
    },
    "3900-c-app_modification_orchestrator": {
        "title": "AppMod-C: Code Implementer",
        "interpretation": "Your goal is not to **theorize about solutions**, but to **generate and implement** the precise code modifications within the specified files, strictly adhering to the detailed change specification and established coding guidelines. Execute as:",
        "transformation": "`{role=code_modification_generator; input=[detailed_specification:json_object, current_file_contents:map_str_str]; process=[iterate_through_affected_files_in_spec(), apply_defined_code_logic_changes(), generate_new_code_snippets_as_required(), integrate_changes_into_existing_file_structure(), ensure_minimal_necessary_changes_for_target_functionality()]; constraints=[strictly_follow_gemini_sdk_guidelines(), maintain_code_style_of_existing_files_where_possible(), do_not_introduce_out_of_scope_changes()]; requirements=[generated_code_must_be_clean_readable_and_directly_implement_spec(), all_modifications_traceable_to_specification_items()]; output={proposed_file_modifications:map_str_str}}`"
    },
    "3900-d-app_modification_orchestrator": {
        "title": "AppMod-D: Change Validator",
        "interpretation": "Your goal is not to **assume correctness**, but to **rigorously validate** the proposed code modifications against the original specification, Gemini API guidelines, frontend best practices (including accessibility, responsiveness, performance), and ensure changes are robust and error-free. Execute as:",
        "transformation": "`{role=modification_quality_assurance_engine; input=[proposed_file_modifications:map_str_str, detailed_specification:json_object, original_file_contents:map_str_str]; process=[verify_functional_requirements_met_from_spec(), check_adherence_to_gemini_api_rules(), lint_code_for_style_and_errors(), assess_for_responsiveness_and_accessibility_issues(), compare_against_frontend_performance_best_practices(), identify_any_regressions_or_unintended_side_effects()]; constraints=[validation_must_be_objective_based_on_defined_criteria(), cover_both_functional_and_non_functional_aspects()]; requirements=[comprehensive_validation_report_or_confirmation(), ensure_no_guideline_violations(), changes_must_be_robust_and_production_ready()]; output={validated_file_modifications:map_str_str, quality_assurance_passed:bool}}`"
    },
    "3900-e-app_modification_orchestrator": {
        "title": "AppMod-E: Output Formatter & Reporter",
        "interpretation": "Your goal is not to **simply output data**, but to **meticulously format** the validated code modifications into the standard XML structure and, if the user's initial directive implies a need for explanation, compose a concise conversational summary of the changes made. Execute as:",
        "transformation": "`{role=final_output_assembler; input=[validated_file_modifications:map_str_str, original_user_directive:str]; process=[construct_xml_payload_for_each_modified_file(), assemble_final_xml_changes_block(), analyze_original_directive_for_summary_need(), if_summary_needed_generate_concise_explanation_of_changes()]; constraints=[xml_output_must_strictly_adhere_to_the_defined_schema(), summary_must_be_brief_and_directly_related_to_changes_made(), if_no_code_change_no_xml_output()]; requirements=[accurate_xml_representation_of_all_changes(), summary_is_natural_language_and_helpful_when_provided(), adhere_to_conditional_summary_generation_logic()]; output={updated_app_representation_xml:str, optional_conversational_summary:str}}`"
    },

    # ---
    "3013-a-name_of_sequence": {
        "title": "xxxxxxxxxxxxxxxxxxxxxxxx",
        "interpretation": "yyyyyyyyyyyyyyy",
        "transformation": "zzzzzzzzzzzzzzz"
    },
    "3013-b-name_of_sequence": {
        "title": "xxxxxxxxxxxxxxxxxxxxxxxx",
        "interpretation": "yyyyyyyyyyyyyyy",
        "transformation": "zzzzzzzzzzzzzzz"
    },
    "3013-c-name_of_sequence": {
        "title": "xxxxxxxxxxxxxxxxxxxxxxxx",
        "interpretation": "yyyyyyyyyyyyyyy",
        "transformation": "zzzzzzzzzzzzzzz"
    },
    "3013-d-name_of_sequence": {
        "title": "xxxxxxxxxxxxxxxxxxxxxxxx",
        "interpretation": "yyyyyyyyyyyyyyy",
        "transformation": "zzzzzzzzzzzzzzz"
    },
    "3013-e-name_of_sequence": {
        "title": "xxxxxxxxxxxxxxxxxxxxxxxx",
        "interpretation": "yyyyyyyyyyyyyyy",
        "transformation": "zzzzzzzzzzzzzzz"
    },
    "3013-f-name_of_sequence": {
        "title": "xxxxxxxxxxxxxxxxxxxxxxxx",
        "interpretation": "yyyyyyyyyyyyyyy",
        "transformation": "zzzzzzzzzzzzzzz"
    },


}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3900, 3999),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
