#!/usr/bin/env python3
"""
Centralized configuration for AI Systems
Contains all constants, defaults, and configuration settings
"""

import os
import sys
from pathlib import Path

# =============================================================================
# PATH CONFIGURATION
# =============================================================================

def get_script_dir():
    """Get current script directory."""
    return os.path.dirname(os.path.abspath(__file__))

# Default paths
SCRIPT_DIR = get_script_dir()
DEFAULT_OUTPUT_DIR = os.path.join(SCRIPT_DIR, "output")

# =============================================================================
# MODEL CONFIGURATION
# =============================================================================

# Model Registry (maps user-friendly names to actual LiteLLM model IDs)
MODEL_REGISTRY = {
    # OpenAI
    "gpt-3.5-turbo": "gpt-3.5-turbo",
    "gpt-3.5-turbo-instruct": "gpt-3.5-turbo-instruct",
    "gpt-4": "gpt-4",
    "gpt-4-turbo": "gpt-4-turbo",
    "gpt-4.1": "gpt-4.1",
    "gpt-4o": "gpt-4o",
    "o3-mini": "o3-mini",
    # Anthropic
    "claude-3-opus": "anthropic/claude-3-opus-20240229",
    "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
    "claude-3-haiku": "anthropic/claude-3-haiku-20240307",
    "claude-3.7-sonnet": "openrouter/anthropic/claude-3.7-sonnet:beta",
    "claude-opus-4-20250514": "claude-opus-4-20250514",
    "claude-sonnet-4-20250514": "claude-sonnet-4-20250514",
    # Google
    "gemini-pro": "gemini/gemini-1.5-pro",
    "gemini-flash": "gemini/gemini-1.5-flash-latest",
    "gemini-2-flash": "gemini/gemini-2.0-flash",
    "gemini-2.5-pro": "gemini/gemini-2.5-pro-preview-03-25",
    # Deepseek
    "deepseek-reasoner": "deepseek/deepseek-reasoner",
    "deepseek-coder": "deepseek/deepseek-coder",
    "deepseek-chat": "deepseek/deepseek-chat",
}

# Provider Selection
PROVIDERS = {
    "anthropic": "anthropic",
    "deepseek": "deepseek",
    "google": "google",
    "openai": "openai"
}

# Default provider
DEFAULT_PROVIDER = "openai"

# Note: MODEL_CONFIG moved to main.py Config class since it's only used there

# =============================================================================
# SEQUENCE CONFIGURATION
# =============================================================================

# Default sequences for different purposes
DEFAULT_SEQUENCES = {
    "general": "3000",
    "analysis": "3031",
    "enhancement": "3100",
    "critique": "3022"
}

# Stage configuration
STAGE_CONFIG = {
    1: {
        "name": "Prototyping & Testing",
        "range": (1000, 1999),
        "auto_id": True,
        "description": "Rapid iteration and experimentation"
    },
    2: {
        "name": "Validated & Unplaced",
        "range": (2000, 2999),
        "auto_id": False,
        "description": "Validated templates awaiting categorization"
    },
    3: {
        "name": "Production Ready",
        "range": (3000, 3999),
        "auto_id": False,
        "description": "Finalized, production-ready templates"
    }
}

# =============================================================================
# EXECUTION DEFAULTS
# =============================================================================

# Default execution settings
EXECUTION_DEFAULTS = {
    "chain_mode": True,
    "show_inputs": True,
    "show_system_instructions": True,  # Show system instructions by default
    "show_responses": True,
    "minified_output": False,
    "temperature": None,
    "max_tokens": None
}

# LiteLLM configuration
LITELLM_CONFIG = {
    "drop_params": True,
    "num_retries": 3,
    "request_timeout": 120,
    "set_verbose": False,
    "callbacks": []
}

# =============================================================================
# FILE NAMING PATTERNS
# =============================================================================

# Output filename patterns
FILENAME_PATTERNS = {
    "history": "history--{timestamp}--{sequence_id}--{model_tag}.json",
    "timestamp_format": "%Y.%m.%d-kl.%H.%M.%S"
}

# File sanitization replacements
FILENAME_SANITIZATION = {
    "|": "+", ":": "-", "?": "_", "*": "_",
    "<": "_", ">": "_", "\"": "_", "/": "_", "\\": "_"
}

# =============================================================================
# INTERACTIVE CLI CONFIGURATION
# =============================================================================

# Interactive CLI specific settings
INTERACTIVE_CONFIG = {
    "recent_executions_limit": 10,
    "generator_preview_limit": 5,
    "recommended_sequences": ["3000", "3031", "3100"],
    "recommended_models": ["gpt-4o", "claude-3-sonnet"]
}

# =============================================================================
# TEMPLATE SYSTEM CONFIGURATION
# =============================================================================

# Template catalog settings
CATALOG_CONFIG = {
    "format": "md",
    "source_directories": ["stage1/md", "stage2/md", "stage3/md"],
    "generator_directories": ["stage1/generators", "stage2/generators", "stage3/generators"]
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def get_stage_name(stage_num):
    """Get descriptive name for a stage."""
    config = STAGE_CONFIG.get(stage_num)
    if config:
        return f"Stage {stage_num}: {config['name']} ({config['range'][0]}-{config['range'][1]})"
    return f"Stage {stage_num}"

def get_stage_from_id(seq_id):
    """Extract stage number from sequence ID."""
    try:
        num = int(seq_id)
        for stage, config in STAGE_CONFIG.items():
            start, end = config["range"]
            if start <= num <= end:
                return stage
        return 0
    except (ValueError, TypeError):
        return 0

def get_default_model(provider=None):
    """Get default model - simplified for shared use."""
    # Simple default for shared usage (interactive CLI, etc.)
    return "gpt-3.5-turbo"
