
    # SEQ:3004
    default_prompt = """[SEQ:3004|3003] ### File Structure\n\n```\n├── 01_core_objectives.md\n├── 02_company_profile.md\n├── 03_legal_framework.md\n├── 04_content_structure.md\n├── 05_design_principles.md\n├── 06_implementation_insights.md\n├── 07_contract_template.md\n└── README.md\n```\n\n---\n\n#### `01_core_objectives.md`\n\n```markdown\n    # Core Objectives\n    \n    ## Primary Goal\n    Generate legally compliant Norwegian employment contracts (arbeidskontrakt) for Ringerike Landskap AS that meet all requirements while fitting on a single A4 page.\n    \n    ## Legal Compliance Requirements\n    - Must satisfy Arbeidsmiljøloven § 14-6 minimum requirements\n    - Include all mandatory sections for Norwegian employment law\n    - Support both permanent and temporary/seasonal employment types\n    - Maintain legal precision while ensuring readability\n    \n    ## Business Context\n    - **Company**: Ringerike Landskap AS (landscaping/construction)\n    - **Industry**: <PERSON>leggsgartner (landscape gardening) and grun<PERSON>beid (groundwork)\n    - **Work locations**: Ringerike and surrounding areas, project-based\n    - **Employment patterns**: Both permanent staff and seasonal workers\n    \n    ## Format Constraints\n    - Single A4 page including signatures\n    - Professional appearance suitable for legal documents\n    - Clear structure for both employer and employee understanding\n    - Flexible enough to accommodate different employment scenarios\n    \n    ## Success Criteria\n    - Legal compliance verified\n    - Practical usability for HR processes\n    - Professional presentation\n    - Efficient generation workflow\n```\n\n---\n\n#### `02_company_profile.md`\n\n```markdown\n    # Company Profile: Ringerike Landskap AS\n    \n    ## Official Company Information\n    - **Name**: Ringerike Landskap AS\n    - **Organization Number**: ***********\n    - **Address**: Birchs vei 7, 3530 Røyse\n    - **Business Type**: Anleggsgartner og maskinentreprenør\n    \n    ## Business Operations\n    - **Primary Services**: Landscaping, groundwork, construction support\n    - **Typical Projects**: Private outdoor spaces, retaining walls, drainage, machine contractor work\n    - **Geographic Scope**: Ringerike and surrounding municipalities\n    - **Work Pattern**: Project-based with seasonal variations\n    \n    ## Employment Characteristics\n    - **Typical Roles**: Anleggsgartner, grunnarbeider, fagarbeider\n    - **Work Schedule**: 37.5 hours/week, typically 07:00-15:00\n    - **Compensation Structure**: Hourly wage (baseline 300 NOK/hour)\n    - **Travel Requirements**: Use of personal vehicles with mileage compensation\n    - **Seasonal Considerations**: Higher activity in construction season\n    \n    ## Industry Context\n    - **Sector**: Construction and landscaping services\n    - **Regulatory Environment**: Norwegian labor law, safety regulations\n    - **Professional Requirements**: HMS training, equipment certification\n    - **Market Position**: Growing regional contractor\n```\n\n---\n\n#### `03_legal_framework.md`\n\n```markdown\n    # Legal Framework for Norwegian Employment Contracts\n    \n    ## Arbeidsmiljøloven § 14-6 Requirements\n    Mandatory contract elements that must be included:\n    \n    ### 1. Party Information\n    - Full names and addresses of employer and employee\n    - Organization number for employer\n    - Personal identification for employee\n    \n    ### 2. Employment Details\n    - Job title and description of work\n    - Start date of employment\n    - Duration (if temporary employment)\n    - Justification for temporary employment (if applicable)\n    - Probation period terms (if applicable)\n    \n    ### 3. Workplace Information\n    - Primary work location\n    - Indication if work is performed at multiple locations\n    - Travel requirements and compensation\n    \n    ### 4. Working Conditions\n    - Weekly working hours\n    - Daily schedule and break arrangements\n    - Overtime compensation terms\n    - Flexibility arrangements\n    \n    ### 5. Compensation Structure\n    - Salary amount and payment method\n    - Payment schedule and dates\n    - Additional compensation (overtime, travel, etc.)\n    - Pension and insurance arrangements\n    \n    ### 6. Leave and Benefits\n    - Vacation entitlement (5 weeks standard)\n    - Vacation pay percentage (12%)\n    - Sick leave arrangements\n    - Other statutory benefits\n    \n    ### 7. Termination Provisions\n    - Notice periods for both parties\n    - Termination procedures\n    - Special provisions for probation period\n    \n    ### 8. Additional Terms\n    - Collective agreement status\n    - Confidentiality requirements\n    - Equipment and safety obligations\n    - Regulatory compliance references\n    \n    ## Key Legal References\n    - **Arbeidsmiljøloven**: Primary employment law\n    - **Ferieloven**: Vacation and vacation pay regulations\n    - **Folketrygdloven**: Social security and benefits\n    - **Tariffavtaler**: Collective agreements (if applicable)\n```\n\n---\n\n#### `04_content_structure.md`\n\n```markdown\n    # Contract Content Structure\n    \n    ## Validated Section Organization\n    Based on legal requirements and practical testing:\n    \n    ### Header Section\n    - Contract title\n    - Company information block\n    - Employee information block (with fill-in fields)\n    \n    ### 1. Employment Relationship (Ansettelsesforhold)\n    - Start date\n    - Employment type (permanent/temporary)\n    - Duration and justification (if temporary)\n    - Probation period terms\n    \n    ### 2. Workplace (Arbeidssted)\n    - Primary location: Ringerike and surrounding areas\n    - Project-based work arrangement\n    - Meeting point coordination\n    \n    ### 3. Position and Tasks (Stilling og oppgaver)\n    - Job title field\n    - Core responsibilities: landscaping and groundwork\n    - Additional duties within company scope\n    \n    ### 4. Working Hours (Arbeidstid)\n    - Standard: 37.5 hours/week\n    - Schedule: 07:00-15:00 typical\n    - Break arrangements per AML\n    - Flexibility provisions\n    \n    ### 5. Salary and Compensation (Lønn og godtgjørelse)\n    - Hourly rate: 300 NOK baseline\n    - Payment date: 5th of each month\n    - Overtime provisions per AML § 10-6\n    - Travel compensation: state rates\n    \n    ### 6. Vacation and Vacation Pay (Ferie og feriepenger)\n    - 5 weeks vacation per ferieloven\n    - 12% vacation pay\n    - Payment timing for short-term employment\n    \n    ### 7. Termination (Oppsigelse)\n    - Probation period: 14 days\n    - Post-probation: 1 month mutual\n    \n    ### 8. Miscellaneous (Diverse)\n    - Equipment provision by employer\n    - Instruction compliance\n    - No collective agreement status\n    \n    ### Signature Section\n    - Date and location\n    - Employer signature line\n    - Employee signature line\n```\n\n---\n\n#### `05_design_principles.md`\n\n```markdown\n    # Design Principles for Contract Generation\n    \n    ## Content-First Approach\n    - **Markdown Foundation**: Start with structured markdown for content clarity\n    - **Format Flexibility**: Enable multiple output formats (HTML, PDF, DOCX) from single source\n    - **Legal Precision**: Prioritize accuracy and compliance over aesthetic concerns\n    - **Readability**: Ensure contracts are accessible to both legal and non-legal readers\n    \n    ## Structural Guidelines\n    \n    ### Brevity and Clarity\n    - One A4 page maximum including signatures\n    - Essential information only - eliminate redundancy\n    - Clear section headers and logical flow\n    - Concise language while maintaining legal validity\n    \n    ### Visual Organization\n    - **Tables**: Use markdown tables for structured data presentation\n    - **Emphasis**: Bold for critical terms and amounts\n    - **Separation**: Clear visual breaks between sections\n    - **Fill-in Fields**: Consistent formatting for variable content\n    \n    ### Modularity\n    - **Reusable Components**: Standardized sections that can be combined\n    - **Variable Content**: Clear separation of fixed vs. customizable elements\n    - **Template Logic**: Support for conditional content (permanent vs. temporary)\n    - **Validation Points**: Built-in checks for required information\n    \n    ## Technical Considerations\n    \n    ### Markdown Advantages\n    - Version control friendly\n    - Human readable in source form\n    - Multiple rendering options\n    - Easy content iteration and refinement\n    - Separation of content from presentation\n    \n    ### Output Format Flexibility\n    - **HTML**: Web preview and browser printing\n    - **PDF**: Official document archival\n    - **DOCX**: Microsoft Word compatibility\n    - **Plain Text**: Fallback and accessibility\n    \n    ### User Experience\n    - **Interactive Input**: Guided data collection\n    - **Validation**: Real-time checking of required fields\n    - **Preview**: Show formatted result before finalization\n    - **Export Options**: Multiple format choices based on need\n    \n    ## Quality Assurance\n    - **Legal Review**: Regular validation against current law\n    - **Practical Testing**: Real-world usage verification\n    - **Stakeholder Feedback**: Input from HR and legal users\n    - **Continuous Improvement**: Iterative refinement based on usage patterns\n```\n\n---\n\n#### `06_implementation_insights.md`\n\n```markdown\n    # Implementation Insights from Previous Iterations\n    \n    ## Evolution Pattern Analysis\n    \n    ### v1-v2: Over-Engineering Phase\n    - **Lesson**: Complex PDF generation frameworks created unnecessary complexity\n    - **Issue**: Multiple library dependencies without clear benefit\n    - **Result**: Functional but overly complicated solutions\n    \n    ### v3-v4: Content Refinement Phase\n    - **Breakthrough**: Markdown-first approach for content development\n    - **Success**: Clear separation of content from presentation concerns\n    - **Validation**: Legal content structure solidified through iteration\n    \n    ### v5: Simplification Success\n    - **Achievement**: Working solution with minimal dependencies\n    - **Technology**: python-docx proved sufficient for DOCX generation\n    - **User Experience**: Interactive CLI provided practical usability\n    \n    ## Key Technical Learnings\n    \n    ### What Works\n    - **Single Purpose Libraries**: python-docx for DOCX, simple and reliable\n    - **Interactive Prompts**: User-friendly data collection\n    - **Template-Based Generation**: Structured approach to content insertion\n    - **Minimal Dependencies**: Fewer moving parts, more reliable operation\n    \n    ### What Doesn't Work\n    - **Complex PDF Engines**: Over-engineered solutions for simple requirements\n    - **Multiple Format Libraries**: Unnecessary complexity for single output need\n    - **Analysis Paralysis**: Extensive reverse-engineering without clear benefit\n    - **Framework Building**: Creating abstractions before understanding requirements\n    \n    ## Strategic Insights\n    \n    ### Simplicity Principle\n    - Start with the simplest solution that meets requirements\n    - Add complexity only when clearly justified\n    - Prefer proven, stable libraries over cutting-edge alternatives\n    - Focus on user needs rather than technical elegance\n    \n    ### Content-First Development\n    - Establish legal content accuracy before technical implementation\n    - Use markdown for rapid content iteration\n    - Separate content structure from output formatting\n    - Validate with stakeholders early and often\n    \n    ### User-Centered Design\n    - Interactive mode essential for practical adoption\n    - Clear error messages and guidance\n    - Flexible input handling (optional vs. required fields)\n    - Multiple output options to meet different use cases\n    \n    ## Recommended v6 Approach\n    \n    ### Phase 1: Content Foundation\n    - Perfect markdown template with all legal requirements\n    - Validate content structure with legal review\n    - Test readability and completeness\n    \n    ### Phase 2: Generation Logic\n    - Implement simple template substitution\n    - Add interactive data collection\n    - Create validation and error handling\n    \n    ### Phase 3: Output Formats\n    - Start with HTML rendering for preview\n    - Add PDF generation if needed\n    - Consider DOCX for compatibility\n    - Maintain markdown as source of truth\n    \n    ### Success Metrics\n    - Legal compliance verified\n    - User adoption in real scenarios\n    - Maintenance simplicity\n    - Output quality and professionalism\n```\n\n---\n\n#### `07_contract_template.md`\n\n```markdown\n    # ARBEIDSAVTALE\n    \n    ## Parter\n    \n    | **Arbeidsgiver** | **Arbeidstaker** |\n    |------------------|------------------|\n    | **Ringerike Landskap AS** | **Navn:** _________________________ |\n    | **Org.nr:** *********** | **Adresse:** ______________________ |\n    | **Adresse:** Birchs vei 7, 3530 Røyse | **Fødselsdato:** ___________________ |\n    \n    ---\n    \n    ## 1. Ansettelsesforhold\n    \n    | Element | Detaljer |\n    |---------|----------|\n    | **Startdato** | _________________________ |\n    | **Type** | ☐ Fast ☐ Midlertidig t.o.m. _____________ |\n    | **Grunnlag** (hvis midlertidig) | _________________________ |\n    | **Prøvetid** | ☐ Ingen ☐ _____ måneder (maks 6) |\n    \n    ---\n    \n    ## 2. Arbeidssted\n    \n    Ringerike og omegn, oppmøtested etter prosjekt.\n    \n    ---\n    \n    ## 3. Stilling og oppgaver\n    \n    | **Stilling** | _________________________________ |\n    |--------------|-----------------------------------|\n    | **Oppgaver** | Anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten. |\n    \n    ---\n    \n    ## 4. Arbeidstid\n    \n    37,5 t/uke, normalt kl. 07–15. Pauser og fleksibilitet ihht. AML.\n    \n    ---\n    \n    ## 5. Lønn og godtgjørelse\n    \n    | Element | Detaljer |\n    |---------|----------|\n    | **Timesats** | Kr 300,- |\n    | **Utbetaling** | Den 5. hver måned |\n    | **Overtid** | Etter AML § 10-6 |\n    | **Kjøring** | Statens satser |\n    \n    ---\n    \n    ## 6. Ferie og feriepenger\n    \n    5 uker ferie iht. ferieloven. Feriepenger 12%. Utbetales ved fratreden ved kort ansettelse.\n    \n    ---\n    \n    ## 7. Oppsigelse\n    \n    | Periode | Frist |\n    |---------|-------|\n    | **I prøvetid** | 14 dager |\n    | **Etter prøvetid** | 1 måned gjensidig |\n    \n    ---\n    \n    ## 8. Diverse\n    \n    Arbeidstaker følger instrukser, arbeidsgiver leverer arbeidstøy/verktøy. Ingen tariffavtale pr. dato.\n    \n    ---\n    \n    ## Signatur\n    \n    | **Sted/dato:** Røyse, _____________ | |\n    |-----------------------------------|---|\n    | **Arbeidsgiver:** ________________ | **Arbeidstaker:** ________________ |\n    \n    ---\n    \n    *Arbeidsavtale i henhold til arbeidsmiljøloven. Begge parter beholder kopi.*\n```\n\n---\n\n#### `README.md`\n\n```markdown\n    # Arbeidskontrakt v6 - Markdown-First Foundation\n    \n    ## Overview\n    \n    This directory contains the distilled essence of the arbeidskontrakt project, focusing on a markdown-first approach for generating Norwegian employment contracts for Ringerike Landskap AS.\n    \n    ## File Structure (Sequential Order)\n    \n    1. **`01_core_objectives.md`** - Primary goals and success criteria\n    2. **`02_company_profile.md`** - Ringerike Landskap AS business context\n    3. **`03_legal_framework.md`** - Norwegian employment law requirements\n    4. **`04_content_structure.md`** - Validated contract section organization\n    5. **`05_design_principles.md`** - Guidelines for implementation approach\n    6. **`06_implementation_insights.md`** - Lessons learned from v1-v5\n    7. **`07_contract_template.md`** - Foundational markdown contract template\n    \n    ## Key Principles\n    \n    1. **Content First**: Perfect the markdown content before considering output formats\n    2. **Legal Compliance**: Meet all Arbeidsmiljøloven § 14-6 requirements\n    3. **Single Page**: Fit complete contract on one A4 page including signatures\n    4. **Flexibility**: Support both permanent and temporary employment\n    5. **Simplicity**: Learn from v5's success - avoid over-engineering\n    \n    ## Strategic Value\n    \n    - **Decontextualized**: All content abstracted from implementation details\n    - **Modular**: Each file addresses a single conceptual component\n    - **Actionable**: Ready for immediate markdown-based development\n    - **Future-Resilient**: Foundation supports multiple output formats\n    - **Chain-Expandable**: Structured for systematic enhancement\n    \n    ## Evolution Summary\n    \n    - **v1-v2**: Over-engineered PDF solutions\n    - **v3-v4**: Markdown prototyping and content refinement  \n    - **v5**: Simple python-docx success\n    - **v6**: Markdown-first foundation with format flexibility\n    \n    ## Next Steps\n    \n    This foundation is prepared for:\n    - Content refinement and legal validation\n    - Template variable substitution logic\n    - Multiple output format generation (HTML, PDF, DOCX)\n    - Interactive data collection interface\n    \n    **The distilled wisdom**: Start simple, focus on content accuracy, and build only what's needed.\n```\n"""
    default_prompt = """[SEQ:3004|3002] Intercept every input as a stream of operative vectors—never as a mere question or passive data"""
    default_prompt = """[SEQ:3004|3100] - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions."""
    default_prompt = """[SEQ:3004|3100] - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame."""
    default_prompt = """[SEQ:3004|3100] Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction."""
    default_prompt =  """[SEQ:3004|3003] ### File Structure\n\n```\n├── 01_core_objectives.md\n├── 02_company_profile.md\n├── 03_legal_framework.md\n├── 04_content_structure.md\n├── 05_design_principles.md\n├── 06_implementation_insights.md\n├── 07_contract_template.md\n└── README.md\n```\n\n---\n\n#### `01_core_objectives.md`\n\n```markdown\n    # Core Objectives\n    \n    ## Primary Goal\n    Generate legally compliant Norwegian employment contracts (arbeidskontrakt) for Ringerike Landskap AS that meet all requirements while fitting on a single A4 page.\n    \n    ## Legal Compliance Requirements\n    - Must satisfy Arbeidsmiljøloven § 14-6 minimum requirements\n    - Include all mandatory sections for Norwegian employment law\n    - Support both permanent and temporary/seasonal employment types\n    - Maintain legal precision while ensuring readability\n    \n    ## Business Context\n    - **Company**: Ringerike Landskap AS (landscaping/construction)\n    - **Industry**: Anleggsgartner (landscape gardening) and grunnarbeid (groundwork)\n    - **Work locations**: Ringerike and surrounding areas, project-based\n    - **Employment patterns**: Both permanent staff and seasonal workers\n    \n    ## Format Constraints\n    - Single A4 page including signatures\n    - Professional appearance suitable for legal documents\n    - Clear structure for both employer and employee understanding\n    - Flexible enough to accommodate different employment scenarios\n    \n    ## Success Criteria\n    - Legal compliance verified\n    - Practical usability for HR processes\n    - Professional presentation\n    - Efficient generation workflow\n```\n\n---\n\n#### `02_company_profile.md`\n\n```markdown\n    # Company Profile: Ringerike Landskap AS\n    \n    ## Official Company Information\n    - **Name**: Ringerike Landskap AS\n    - **Organization Number**: ***********\n    - **Address**: Birchs vei 7, 3530 Røyse\n    - **Business Type**: Anleggsgartner og maskinentreprenør\n    \n    ## Business Operations\n    - **Primary Services**: Landscaping, groundwork, construction support\n    - **Typical Projects**: Private outdoor spaces, retaining walls, drainage, machine contractor work\n    - **Geographic Scope**: Ringerike and surrounding municipalities\n    - **Work Pattern**: Project-based with seasonal variations\n    \n    ## Employment Characteristics\n    - **Typical Roles**: Anleggsgartner, grunnarbeider, fagarbeider\n    - **Work Schedule**: 37.5 hours/week, typically 07:00-15:00\n    - **Compensation Structure**: Hourly wage (baseline 300 NOK/hour)\n    - **Travel Requirements**: Use of personal vehicles with mileage compensation\n    - **Seasonal Considerations**: Higher activity in construction season\n    \n    ## Industry Context\n    - **Sector**: Construction and landscaping services\n    - **Regulatory Environment**: Norwegian labor law, safety regulations\n    - **Professional Requirements**: HMS training, equipment certification\n    - **Market Position**: Growing regional contractor\n```\n\n---\n\n#### `03_legal_framework.md`\n\n```markdown\n    # Legal Framework for Norwegian Employment Contracts\n    \n    ## Arbeidsmiljøloven § 14-6 Requirements\n    Mandatory contract elements that must be included:\n    \n    ### 1. Party Information\n    - Full names and addresses of employer and employee\n    - Organization number for employer\n    - Personal identification for employee\n    \n    ### 2. Employment Details\n    - Job title and description of work\n    - Start date of employment\n    - Duration (if temporary employment)\n    - Justification for temporary employment (if applicable)\n    - Probation period terms (if applicable)\n    \n    ### 3. Workplace Information\n    - Primary work location\n    - Indication if work is performed at multiple locations\n    - Travel requirements and compensation\n    \n    ### 4. Working Conditions\n    - Weekly working hours\n    - Daily schedule and break arrangements\n    - Overtime compensation terms\n    - Flexibility arrangements\n    \n    ### 5. Compensation Structure\n    - Salary amount and payment method\n    - Payment schedule and dates\n    - Additional compensation (overtime, travel, etc.)\n    - Pension and insurance arrangements\n    \n    ### 6. Leave and Benefits\n    - Vacation entitlement (5 weeks standard)\n    - Vacation pay percentage (12%)\n    - Sick leave arrangements\n    - Other statutory benefits\n    \n    ### 7. Termination Provisions\n    - Notice periods for both parties\n    - Termination procedures\n    - Special provisions for probation period\n    \n    ### 8. Additional Terms\n    - Collective agreement status\n    - Confidentiality requirements\n    - Equipment and safety obligations\n    - Regulatory compliance references\n    \n    ## Key Legal References\n    - **Arbeidsmiljøloven**: Primary employment law\n    - **Ferieloven**: Vacation and vacation pay regulations\n    - **Folketrygdloven**: Social security and benefits\n    - **Tariffavtaler**: Collective agreements (if applicable)\n```\n\n---\n\n#### `04_content_structure.md`\n\n```markdown\n    # Contract Content Structure\n    \n    ## Validated Section Organization\n    Based on legal requirements and practical testing:\n    \n    ### Header Section\n    - Contract title\n    - Company information block\n    - Employee information block (with fill-in fields)\n    \n    ### 1. Employment Relationship (Ansettelsesforhold)\n    - Start date\n    - Employment type (permanent/temporary)\n    - Duration and justification (if temporary)\n    - Probation period terms\n    \n    ### 2. Workplace (Arbeidssted)\n    - Primary location: Ringerike and surrounding areas\n    - Project-based work arrangement\n    - Meeting point coordination\n    \n    ### 3. Position and Tasks (Stilling og oppgaver)\n    - Job title field\n    - Core responsibilities: landscaping and groundwork\n    - Additional duties within company scope\n    \n    ### 4. Working Hours (Arbeidstid)\n    - Standard: 37.5 hours/week\n    - Schedule: 07:00-15:00 typical\n    - Break arrangements per AML\n    - Flexibility provisions\n    \n    ### 5. Salary and Compensation (Lønn og godtgjørelse)\n    - Hourly rate: 300 NOK baseline\n    - Payment date: 5th of each month\n    - Overtime provisions per AML § 10-6\n    - Travel compensation: state rates\n    \n    ### 6. Vacation and Vacation Pay (Ferie og feriepenger)\n    - 5 weeks vacation per ferieloven\n    - 12% vacation pay\n    - Payment timing for short-term employment\n    \n    ### 7. Termination (Oppsigelse)\n    - Probation period: 14 days\n    - Post-probation: 1 month mutual\n    \n    ### 8. Miscellaneous (Diverse)\n    - Equipment provision by employer\n    - Instruction compliance\n    - No collective agreement status\n    \n    ### Signature Section\n    - Date and location\n    - Employer signature line\n    - Employee signature line\n```\n\n---\n\n#### `05_design_principles.md`\n\n```markdown\n    # Design Principles for Contract Generation\n    \n    ## Content-First Approach\n    - **Markdown Foundation**: Start with structured markdown for content clarity\n    - **Format Flexibility**: Enable multiple output formats (HTML, PDF, DOCX) from single source\n    - **Legal Precision**: Prioritize accuracy and compliance over aesthetic concerns\n    - **Readability**: Ensure contracts are accessible to both legal and non-legal readers\n    \n    ## Structural Guidelines\n    \n    ### Brevity and Clarity\n    - One A4 page maximum including signatures\n    - Essential information only - eliminate redundancy\n    - Clear section headers and logical flow\n    - Concise language while maintaining legal validity\n    \n    ### Visual Organization\n    - **Tables**: Use markdown tables for structured data presentation\n    - **Emphasis**: Bold for critical terms and amounts\n    - **Separation**: Clear visual breaks between sections\n    - **Fill-in Fields**: Consistent formatting for variable content\n    \n    ### Modularity\n    - **Reusable Components**: Standardized sections that can be combined\n    - **Variable Content**: Clear separation of fixed vs. customizable elements\n    - **Template Logic**: Support for conditional content (permanent vs. temporary)\n    - **Validation Points**: Built-in checks for required information\n    \n    ## Technical Considerations\n    \n    ### Markdown Advantages\n    - Version control friendly\n    - Human readable in source form\n    - Multiple rendering options\n    - Easy content iteration and refinement\n    - Separation of content from presentation\n    \n    ### Output Format Flexibility\n    - **HTML**: Web preview and browser printing\n    - **PDF**: Official document archival\n    - **DOCX**: Microsoft Word compatibility\n    - **Plain Text**: Fallback and accessibility\n    \n    ### User Experience\n    - **Interactive Input**: Guided data collection\n    - **Validation**: Real-time checking of required fields\n    - **Preview**: Show formatted result before finalization\n    - **Export Options**: Multiple format choices based on need\n    \n    ## Quality Assurance\n    - **Legal Review**: Regular validation against current law\n    - **Practical Testing**: Real-world usage verification\n    - **Stakeholder Feedback**: Input from HR and legal users\n    - **Continuous Improvement**: Iterative refinement based on usage patterns\n```\n\n---\n\n#### `06_implementation_insights.md`\n\n```markdown\n    # Implementation Insights from Previous Iterations\n    \n    ## Evolution Pattern Analysis\n    \n    ### v1-v2: Over-Engineering Phase\n    - **Lesson**: Complex PDF generation frameworks created unnecessary complexity\n    - **Issue**: Multiple library dependencies without clear benefit\n    - **Result**: Functional but overly complicated solutions\n    \n    ### v3-v4: Content Refinement Phase\n    - **Breakthrough**: Markdown-first approach for content development\n    - **Success**: Clear separation of content from presentation concerns\n    - **Validation**: Legal content structure solidified through iteration\n    \n    ### v5: Simplification Success\n    - **Achievement**: Working solution with minimal dependencies\n    - **Technology**: python-docx proved sufficient for DOCX generation\n    - **User Experience**: Interactive CLI provided practical usability\n    \n    ## Key Technical Learnings\n    \n    ### What Works\n    - **Single Purpose Libraries**: python-docx for DOCX, simple and reliable\n    - **Interactive Prompts**: User-friendly data collection\n    - **Template-Based Generation**: Structured approach to content insertion\n    - **Minimal Dependencies**: Fewer moving parts, more reliable operation\n    \n    ### What Doesn't Work\n    - **Complex PDF Engines**: Over-engineered solutions for simple requirements\n    - **Multiple Format Libraries**: Unnecessary complexity for single output need\n    - **Analysis Paralysis**: Extensive reverse-engineering without clear benefit\n    - **Framework Building**: Creating abstractions before understanding requirements\n    \n    ## Strategic Insights\n    \n    ### Simplicity Principle\n    - Start with the simplest solution that meets requirements\n    - Add complexity only when clearly justified\n    - Prefer proven, stable libraries over cutting-edge alternatives\n    - Focus on user needs rather than technical elegance\n    \n    ### Content-First Development\n    - Establish legal content accuracy before technical implementation\n    - Use markdown for rapid content iteration\n    - Separate content structure from output formatting\n    - Validate with stakeholders early and often\n    \n    ### User-Centered Design\n    - Interactive mode essential for practical adoption\n    - Clear error messages and guidance\n    - Flexible input handling (optional vs. required fields)\n    - Multiple output options to meet different use cases\n    \n    ## Recommended v6 Approach\n    \n    ### Phase 1: Content Foundation\n    - Perfect markdown template with all legal requirements\n    - Validate content structure with legal review\n    - Test readability and completeness\n    \n    ### Phase 2: Generation Logic\n    - Implement simple template substitution\n    - Add interactive data collection\n    - Create validation and error handling\n    \n    ### Phase 3: Output Formats\n    - Start with HTML rendering for preview\n    - Add PDF generation if needed\n    - Consider DOCX for compatibility\n    - Maintain markdown as source of truth\n    \n    ### Success Metrics\n    - Legal compliance verified\n    - User adoption in real scenarios\n    - Maintenance simplicity\n    - Output quality and professionalism\n```\n\n---\n\n#### `07_contract_template.md`\n\n```markdown\n    # ARBEIDSAVTALE\n    \n    ## Parter\n    \n    | **Arbeidsgiver** | **Arbeidstaker** |\n    |------------------|------------------|\n    | **Ringerike Landskap AS** | **Navn:** _________________________ |\n    | **Org.nr:** *********** | **Adresse:** ______________________ |\n    | **Adresse:** Birchs vei 7, 3530 Røyse | **Fødselsdato:** ___________________ |\n    \n    ---\n    \n    ## 1. Ansettelsesforhold\n    \n    | Element | Detaljer |\n    |---------|----------|\n    | **Startdato** | _________________________ |\n    | **Type** | ☐ Fast ☐ Midlertidig t.o.m. _____________ |\n    | **Grunnlag** (hvis midlertidig) | _________________________ |\n    | **Prøvetid** | ☐ Ingen ☐ _____ måneder (maks 6) |\n    \n    ---\n    \n    ## 2. Arbeidssted\n    \n    Ringerike og omegn, oppmøtested etter prosjekt.\n    \n    ---\n    \n    ## 3. Stilling og oppgaver\n    \n    | **Stilling** | _________________________________ |\n    |--------------|-----------------------------------|\n    | **Oppgaver** | Anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten. |\n    \n    ---\n    \n    ## 4. Arbeidstid\n    \n    37,5 t/uke, normalt kl. 07–15. Pauser og fleksibilitet ihht. AML.\n    \n    ---\n    \n    ## 5. Lønn og godtgjørelse\n    \n    | Element | Detaljer |\n    |---------|----------|\n    | **Timesats** | Kr 300,- |\n    | **Utbetaling** | Den 5. hver måned |\n    | **Overtid** | Etter AML § 10-6 |\n    | **Kjøring** | Statens satser |\n    \n    ---\n    \n    ## 6. Ferie og feriepenger\n    \n    5 uker ferie iht. ferieloven. Feriepenger 12%. Utbetales ved fratreden ved kort ansettelse.\n    \n    ---\n    \n    ## 7. Oppsigelse\n    \n    | Periode | Frist |\n    |---------|-------|\n    | **I prøvetid** | 14 dager |\n    | **Etter prøvetid** | 1 måned gjensidig |\n    \n    ---\n    \n    ## 8. Diverse\n    \n    Arbeidstaker følger instrukser, arbeidsgiver leverer arbeidstøy/verktøy. Ingen tariffavtale pr. dato.\n    \n    ---\n    \n    ## Signatur\n    \n    | **Sted/dato:** Røyse, _____________ | |\n    |-----------------------------------|---|\n    | **Arbeidsgiver:** ________________ | **Arbeidstaker:** ________________ |\n    \n    ---\n    \n    *Arbeidsavtale i henhold til arbeidsmiljøloven. Begge parter beholder kopi.*\n```\n\n---\n\n#### `README.md`\n\n```markdown\n    # Arbeidskontrakt v6 - Markdown-First Foundation\n    \n    ## Overview\n    \n    This directory contains the distilled essence of the arbeidskontrakt project, focusing on a markdown-first approach for generating Norwegian employment contracts for Ringerike Landskap AS.\n    \n    ## File Structure (Sequential Order)\n    \n    1. **`01_core_objectives.md`** - Primary goals and success criteria\n    2. **`02_company_profile.md`** - Ringerike Landskap AS business context\n    3. **`03_legal_framework.md`** - Norwegian employment law requirements\n    4. **`04_content_structure.md`** - Validated contract section organization\n    5. **`05_design_principles.md`** - Guidelines for implementation approach\n    6. **`06_implementation_insights.md`** - Lessons learned from v1-v5\n    7. **`07_contract_template.md`** - Foundational markdown contract template\n    \n    ## Key Principles\n    \n    1. **Content First**: Perfect the markdown content before considering output formats\n    2. **Legal Compliance**: Meet all Arbeidsmiljøloven § 14-6 requirements\n    3. **Single Page**: Fit complete contract on one A4 page including signatures\n    4. **Flexibility**: Support both permanent and temporary employment\n    5. **Simplicity**: Learn from v5's success - avoid over-engineering\n    \n    ## Strategic Value\n    \n    - **Decontextualized**: All content abstracted from implementation details\n    - **Modular**: Each file addresses a single conceptual component\n    - **Actionable**: Ready for immediate markdown-based development\n    - **Future-Resilient**: Foundation supports multiple output formats\n    - **Chain-Expandable**: Structured for systematic enhancement\n    \n    ## Evolution Summary\n    \n    - **v1-v2**: Over-engineered PDF solutions\n    - **v3-v4**: Markdown prototyping and content refinement  \n    - **v5**: Simple python-docx success\n    - **v6**: Markdown-first foundation with format flexibility\n    \n    ## Next Steps\n    \n    This foundation is prepared for:\n    - Content refinement and legal validation\n    - Template variable substitution logic\n    - Multiple output format generation (HTML, PDF, DOCX)\n    - Interactive data collection interface\n    \n    **The distilled wisdom**: Start simple, focus on content accuracy, and build only what's needed.\n```\n"""
    default_prompt =  """[SEQ:3004|3003|3005] Rephrase the intent of this instruction:\n```\nSystem Message Instructions for Optimized Text Restructuring: Given any input text (sentence or paragraph), first decompose it into a sequential list of semantic segments—each list item reflecting a meaningful section of the original content. Analyze the resulting segment list, then intelligently reorder its elements to maximize logical flow, clarity, and overall communicative effectiveness (informed by both syntactic and semantic optimization strategies). Next, synthesize the reordered list into a single, cohesively structured sentence or paragraph that preserves the *core intent* while enhancing readability and impact. Throughout the process, ensure bidirectional resonance: the decomposition illuminates previously hidden structural opportunities for optimization, while the reordering and recombination stages continuously inform and refine the segmentation process for future iterations. Always preserve the essence of the original message, but enact transformative restructuring aimed at delivering an amplified, elegantly expressed representation. Operate synergically: each stage reinforces the other, combining LLM-optimized breakdown, reordering, and synthesis into a seamless, high-efficiency transformation pipeline. Constraints: Maintain fidelity to the original philosophy of both systematic decomposition and optimal rephrasing; avoid introducing conflicting meanings or losing intent; ensure each process amplifies the next. Requirement: Deliver a single instruction set that unifies these steps, achieving a coherent, harmonized operational logic that produces clear, maximally effective rephrasings of any input text.\n```\n\nThe new intent is as follows:\n```\nRedesign the sequence to ensure each step addresses a unique aspect, structuring the process so that each action leads to increased cohesion and the convergence of all steps toward the final output.: Redesign the operational sequence to establish a tightly integrated workflow, ensuring each discrete step exclusively addresses a designated functional dimension. Structure each step to progressively align outputs toward unified convergence at the final deliverable. Maintain clear demarcation of responsibilities across all phases, facilitating seamless interaction between consecutive stages while systematically eliminating redundancy and ambiguity for maximized overall cohesion.\nTEMPLATES = {\n    # 3006: System Instruction Syntax Enforcer\n    "3006-a-instruction_syntax_enforcer": {\n        "title": "Directive Analyzer",\n        "interpretation": "Your goal is not to **interpret** the input instruction, but to **decompose** it into its core operational components and transformation intent. Execute as:",\n        "transformation": "`{role=directive_decomposer; input=[instruction:str]; process=[identify_transformation_purpose(), extract_operational_logic(), isolate_procedural_elements(), map_input_output_relationships()]; constraints=[preserve_technical_terminology(), maintain_operational_hierarchy()]; requirements=[comprehensive_intent_capture(), functional_decomposition()]; output={instruction_components:list}}`"\n    },\n    "3006-b-instruction_syntax_enforcer": {\n        "title": "Canonical Formatter",\n        "interpretation": "Your goal is not to **restructure** the components arbitrarily, but to **reorder** them into the mandatory three-part canonical structure with strict goal negation. Execute as:",\n        "transformation": "`{role=structure_engineer; input=[instruction_components:list]; process=[craft_precise_title(), formulate_strict_goal_negation(), identify_transformation_verb(), eliminate_conversational_elements(), enforce_command_voice()]; constraints=[maintain_component_integrity(), preserve_operational_logic()]; requirements=[structural_coherence(), directive_clarity()]; output={canonical_framework:dict}}`"\n    },\n    "3006-c-instruction_syntax_enforcer": {\n        "title": "Transformation Engineer",\n        "interpretation": "Your goal is not to **describe** the transformation process, but to **synthesize** a precise execution block with typed parameters and function-based process steps. Execute as:",\n        "transformation": "`{role=execution_synthesizer; input=[canonical_framework:dict, instruction_components:list]; process=[define_specific_role_designation(), specify_typed_input_parameters(), construct_ordered_process_functions(), establish_explicit_constraints(), formulate_precise_requirements(), define_typed_output_format()]; constraints=[maintain_function_call_syntax(), ensure_complete_parameter_typing()]; requirements=[process_step_atomicity(), constraint_enforceability()]; output={transformation_block:str}}`"\n    },\n    "3006-d-instruction_syntax_enforcer": {\n        "title": "Compliance Validator",\n        "interpretation": "Your goal is not to **finalize** the template components, but to **refine** them through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as:",\n        "transformation": "`{role=template_optimizer; input=[canonical_framework:dict, transformation_block:str]; process=[integrate_canonical_components(), verify_structural_integrity(), validate_directive_purity(), enhance_operational_power(), eliminate_forbidden_elements(), maximize_bidirectional_synergy()]; constraints=[preserve_core_intent(), maintain_command_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={compliant_template:str}}`"\n    },\n    "3006-e-instruction_syntax_enforcer": {\n        "title": "Template Finalizer",\n        "interpretation": "Your goal is not to **modify** the compliant template, but to **crystallize** it into its most potent, executable form with maximum operational clarity and directive force. Execute as:",\n        "transformation": "`{role=template_finalizer; input=[compliant_template:str, original_instruction:str]; process=[eliminate_residual_ambiguities(), maximize_directive_precision(), enhance_execution_specificity(), verify_complete_compliance(), polish_syntactic_elements()]; constraints=[preserve_absolute_intent_fidelity(), maintain_perfect_structural_integrity()]; requirements=[maximum_operational_potency(), instant_executability()]; output={final_template:str}}`"\n    },\n\n    # 3007: Instruction Template Syntax Enforcer\n    "3007-a-instruction_syntax_enforcer": {\n        "title": "Pattern Extractor",\n        "interpretation": "Your goal is not to **analyze** content, but to **extract** underlying patterns and structural frameworks. Execute as:",\n        "transformation": "`{role=pattern_extractor; input=[content:any]; process=[identify_core_structures(), extract_transformation_vectors(), map_operational_elements(), detect_recurring_patterns()]; constraints=[focus_on_generalizable_elements(), maintain_pattern_integrity()]; requirements=[pattern_reusability(), structural_clarity()]; output={extracted_patterns:dict}}`"\n    },\n    "3007-b-instruction_syntax_enforcer": {\n        "title": "Template Architect",\n        "interpretation": "Your goal is not to **describe** patterns, but to **architect** them into canonical template structures. Execute as:",\n        "transformation": "`{role=template_architect; input=[extracted_patterns:dict]; process=[craft_precise_titles(), formulate_goal_negation_statements(), identify_transformation_verbs(), design_execution_blocks()]; constraints=[adhere_to_canonical_structure(), eliminate_conversational_elements()]; requirements=[structural_compliance(), directive_purity()]; output={template_frameworks:list}}`"\n    },\n    "3007-c-instruction_syntax_enforcer": {\n        "title": "Transformation Composer",\n        "interpretation": "Your goal is not to **outline** frameworks, but to **compose** precise transformation blocks with typed parameters and function-based processes. Execute as:",\n        "transformation": "`{role=transformation_composer; input=[template_frameworks:list]; process=[define_specific_roles(), specify_typed_parameters(), construct_process_functions(), establish_constraints(), formulate_requirements(), define_output_formats()]; constraints=[maintain_function_call_syntax(), ensure_parameter_typing()]; requirements=[process_step_atomicity(), constraint_specificity()]; output={transformation_blocks:list}}`"\n    },\n    "3007-d-instruction_syntax_enforcer": {\n        "title": "Sequence Integrator",\n        "interpretation": "Your goal is not to **combine** components, but to **integrate** them into a unified transformation sequence with bidirectional synergy. Execute as:",\n        "transformation": "`{role=sequence_integrator; input=[template_frameworks:list, transformation_blocks:list]; process=[establish_cross-component_references(), optimize_input_output_flows(), enhance_transformation_synergies(), validate_sequence_integrity()]; constraints=[preserve_component_integrity(), maintain_sequence_coherence()]; requirements=[seamless_integration(), maximum_transformation_power()]; output={integrated_sequence:dict}}`"\n    },\n    "3007-e-instruction_syntax_enforcer": {\n        "title": "Template Crystallizer",\n        "interpretation": "Your goal is not to **refine** sequences, but to **crystallize** them into their most potent, executable form. Execute as:",\n        "transformation": "`{role=template_crystallizer; input=[integrated_sequence:dict]; process=[eliminate_residual_ambiguities(), maximize_directive_precision(), enhance_execution_specificity(), verify_complete_compliance(), polish_syntactic_elements()]; constraints=[preserve_absolute_intent_fidelity(), maintain_perfect_structural_integrity()]; requirements=[maximum_operational_potency(), instant_executability()]; output={crystallized_templates:list}}`"\n    },\n\n    # 3008: Instruction Template Syntax Enforcer\n    "3008-a-instruction_syntax_enforcer": {\n        "title": "Intent Cartographer",\n        "interpretation": "Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:",\n        "transformation": "`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`"\n    },\n    "3008-b-instruction_syntax_enforcer": {\n        "title": "Structural Framework Engineer",\n        "interpretation": "Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:",\n        "transformation": "`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`"\n    },\n    "3008-c-instruction_syntax_enforcer": {\n        "title": "Functional Process Composer",\n        "interpretation": "Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:",\n        "transformation": "`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`"\n    },\n    "3008-d-instruction_syntax_enforcer": {\n        "title": "Boundary Systems Architect",\n        "interpretation": "Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:",\n        "transformation": "`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`"\n    },\n    "3008-e-instruction_syntax_enforcer": {\n        "title": "Integration Orchestrator",\n        "interpretation": "Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:",\n        "transformation": "`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`"\n    },\n    "3008-e-instruction_syntax_enforcer": {\n        "title": "Template Crystallizer",\n        "interpretation": "Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:",\n        "transformation": "`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`"\n    },\n}\n```\n\nThe final rephrased text should be a single paragraph of maximum 1500 characters in total."""
    default_prompt =  """[SEQ:3004|3003|3005] # KONTEKST\n\nGjør det kjent med arbeidskontrakt basert på dokumentet beskrevet nedenfor (inkludert lenker):\n\n    Et annet ord for arbeidskontrakt, er arbeidsavtale. Alle som jobber, som er arbeidstakere, skal ha en arbeidskontrakt. Det gjelder selv om du bare jobber midlertidig. Hvis du og arbeidsgiveren ikke er enige om arbeidstiden din, arbeidsoppgavene eller andre forhold med jobben din, kan dere se i kontrakten. Dere bør signere to like kontrakter, og beholde en del hver. På den måten kan ingen endre avtalen i etterkant.\n\n    ## INNHOLD I ARBEIDSKONTRAKTEN\n\n    Arbeidskontrakten skal være skriftlig. Det er arbeidsgivers ansvar å lage en arbeidskontrakt. Hensikten med kontrakten er at du skal vite hva slags arbeidsoppgaver du har, når du skal jobbe og hvor mye lønn du skal ha. I tillegg skal den inneholde:\n\n    -   Ditt navn og navnet på arbeidsplassen.\n    -   En beskrivelse av arbeidet du skal gjøre, og hvilken stilling du har.\n    -   Når arbeidsforholdet begynner.\n    -   Dersom du har prøvetid, skal det står noe om det også.\n    -   Ferie, feriepenger og hvordan det blir bestemt når du skal ha ferie.\n    -   Regler for oppsigelse.\n    -   Hvor mye lønn du skal få (og eventuelt pensjonsinnbetaling, kostgodtgjørelse og andre ting).\n    -   Når og hvordan lønnen blir utbetalt.\n    -   Hvordan du skal jobbe i løpet av en dag og i løpet av en uke.\n    -   Hvor lange pauser du skal ha.\n    -   Hvis det er fleksitid, må det stå noe om det også.\n    -   Opplysninger om tariffavtaler.\n\n    Hvis du er midlertidig ansatt, skal kontrakten også inneholde informasjon om hvor lenge du skal jobbe. Det må også stå hvorfor du er midlertidig ansatt.\n\n    Ingen arbeidsforhold er like. Noen jobber fem dager i uken, andre jobber bare noen timer hver uke. Derfor er alle arbeidskontrakter forskjellige.\n\n    Hvis du trenger hjelp til å forstå arbeidskontrakten din, kan du kontakte Arbeidstilsynet. De har ansvaret for at alle følger reglene som gjelder for kontrakter. Hos Arbeidstilsynet kan du også finne eksempler på arbeidskontrakter.\n\n    ### Nyttige lenker\n\n    -   Du kan lese mer om hva en arbeidskontrakt skal inneholde på [nettsidene til Arbeidstilsynet](https://www.arbeidstilsynet.no/arbeidsforhold/arbeidsavtale/).\n\n    ## HVEM SKAL HA ARBEIDSKONTRAKT?\n\n    Alle som utfører arbeid skal ha arbeidskontrakt. Den skal være skriftlig. Det er arbeidsgiveren sitt ansvar at det finnes en kontrakt. Det er også vanlig at arbeidsgiveren lager utkastet til arbeidskontrakt. Når du får kontrakten, er det viktig å lese nøye igjennom hele kontrakten. Dersom det er noe du ikke forstår, må du ikke undertegne kontrakten.\n\n    Noen ganger skal man ha et kort arbeidsforhold. Kanskje man bare skal hjelpe til noen uker før jul, eller ha en midlertidig sommerjobb. Da må arbeidskontrakten være inngått før arbeidet starter. Hvis du skal jobbe et sted lenger enn en måned, holder det at arbeidskontrakten er inngått senest en måned etter at du begynte å jobbe. Det er lovens grenser, men det kan likevel være lurt å inngå avtalen med en gang, så blir det ingen uenigheter.\n\n    Noen ganger endrer arbeidsforholdet seg. Kanskje man får ny lønn eller får nye arbeidsoppgaver. Da må kontrakten endres. En enkel måte å gjøre dette på, er å lage vedlegg. Da slipper man skrive en hel kontrakt på nytt.\n\n    ### Nyttige lenker\n\n    -   På nettsidene til Arbeidstilsynet kan du se et eksempel på [arbeidskontrakt på mange forskjellige språk](https://www.arbeidstilsynet.no/arbeidsforhold/arbeidsavtale/maler-for-arbeidsavtaler/).\n\n# SCENARIO\n\nJeg er i ferd med å lage en arbeidskontrakt for en ny ansatt, og vurderer nå finne en bedre måte å gjøre dette på (enn å manuelt editere en .docx fil). Dette er fordi det er et veldig lite hensiktmessig valg (å bruke word, libreoffice, e.l.) når det kommer til fleksibilitet, og representerer et unødvendig mellomledd (f.eks. i kontekst av å generere arbeidskontrakt), men også et svært lite fremtidsrettet valg med tanke at; det er et "dumt" og utdatert program som fortsatt eksisterer nesten utelukkende fordi de dannet markedsdominans, og har en "kundegruppe" som representerer de lengst bak når det kommer til teknologi. Det at man må "lære et verktøy" for å kunne generere en ansettelskontrakt (f.eks. basert på forskjellige maler) er komplett unødvendig, fordi nå har eksplosjonen av utvikling gjort at; alle problemer er allerede løst, nå handler det først og fremst om gode idèer.\n\nJeg er en utvikler, og har kjenskap til alle platformer og språk - nå er mitt mål å definere den beste konseptuelle strukturen for å generere disse arbeidskontraktene via. kode (som kan åpnes i et tekstprogram). Jeg kommer til å bruke mitt nåværende scenario som "pilot", som er å lage en kortfattet ansettelseskontrakt for bruk i firmaet __Ringerike Landskap As__. Kontrakten skal oppfylle minimumskravene i arbeidsmiljøloven § 14-6, samtidig som den i fullstendig versjon kan representered på **èn** A4-side (inkludert underskrift). Før jeg bestemmer meg for hvilken metode jeg kommer til å bruke for den fullstendige prossen, så starter jeg med å lage den i markdown slik at jeg enkelt kan definere teksten først. En fordel med markdown er at det kan genereres html fra de, noe som gjør at det er veldig enkelt å "prototype" på denne måten. Nedenfor har jeg lagt ved essensiell informasjon om firmaet:\n```\n\n### File Structure\n\n```\n├── README.md\n├── 01_core_objectives.md\n├── 02_company_profile.md\n├── 03_legal_framework.md\n├── 04_content_structure.md\n├── 05_design_principles.md\n├── 06_implementation_insights.md\n└── 07_contract_template.md\n```\n\n---\n\n#### `README.md`\n\n```markdown\n    # Arbeidskontrakt v6 - Markdown-First Foundation\n\n    ## Overview\n\n    This directory contains the distilled essence of the arbeidskontrakt project, focusing on a markdown-first approach for generating Norwegian employment contracts for Ringerike Landskap AS.\n\n    ## File Structure (Sequential Order)\n\n    1. **`01_core_objectives.md`** - Primary goals and success criteria\n    2. **`02_company_profile.md`** - Ringerike Landskap AS business context\n    3. **`03_legal_framework.md`** - Norwegian employment law requirements\n    4. **`04_content_structure.md`** - Validated contract section organization\n    5. **`05_design_principles.md`** - Guidelines for implementation approach\n    6. **`06_implementation_insights.md`** - Lessons learned from v1-v5\n    7. **`07_contract_template.md`** - Foundational markdown contract template\n\n    ## Key Principles\n\n    1. **Content First**: Perfect the markdown content before considering output formats\n    2. **Legal Compliance**: Meet all Arbeidsmiljøloven § 14-6 requirements\n    3. **Single Page**: Fit complete contract on one A4 page including signatures\n    4. **Flexibility**: Support both permanent and temporary employment\n    5. **Simplicity**: Learn from v5's success - avoid over-engineering\n\n    ## Strategic Value\n\n    - **Decontextualized**: All content abstracted from implementation details\n    - **Modular**: Each file addresses a single conceptual component\n    - **Actionable**: Ready for immediate markdown-based development\n    - **Future-Resilient**: Foundation supports multiple output formats\n    - **Chain-Expandable**: Structured for systematic enhancement\n\n    ## Evolution Summary\n\n    - **v1-v2**: Over-engineered PDF solutions\n    - **v3-v4**: Markdown prototyping and content refinement\n    - **v5**: Simple python-docx success\n    - **v6**: Markdown-first foundation with format flexibility\n\n    ## Next Steps\n\n    This foundation is prepared for:\n    - Content refinement and legal validation\n    - Template variable substitution logic\n    - Multiple output format generation (HTML, PDF, DOCX)\n    - Interactive data collection interface\n\n    **The distilled wisdom**: Start simple, focus on content accuracy, and build only what's needed.\n```\n\n---\n\n#### `01_core_objectives.md`\n\n```markdown\n    # Core Objectives\n\n    ## Primary Goal\n    Generate legally compliant Norwegian employment contracts (arbeidskontrakt) for Ringerike Landskap AS that meet all requirements while fitting on a single A4 page.\n\n    ## Legal Compliance Requirements\n    - Must satisfy Arbeidsmiljøloven § 14-6 minimum requirements\n    - Include all mandatory sections for Norwegian employment law\n    - Support both permanent and temporary/seasonal employment types\n    - Maintain legal precision while ensuring readability\n\n    ## Business Context\n    - **Company**: Ringerike Landskap AS (landscaping/construction)\n    - **Industry**: Anleggsgartner (landscape gardening) and grunnarbeid (groundwork)\n    - **Work locations**: Ringerike and surrounding areas, project-based\n    - **Employment patterns**: Both permanent staff and seasonal workers\n\n    ## Format Constraints\n    - Single A4 page including signatures\n    - Professional appearance suitable for legal documents\n    - Clear structure for both employer and employee understanding\n    - Flexible enough to accommodate different employment scenarios\n\n    ## Success Criteria\n    - Legal compliance verified\n    - Practical usability for HR processes\n    - Professional presentation\n    - Efficient generation workflow\n```\n\n---\n\n#### `02_company_profile.md`\n\n```markdown\n    # Company Profile: Ringerike Landskap AS\n\n    ## Official Company Information\n    - **Name**: Ringerike Landskap AS\n    - **Organization Number**: ***********\n    - **Address**: Birchs vei 7, 3530 Røyse\n    - **Business Type**: Anleggsgartner og maskinentreprenør\n\n    ## Business Operations\n    - **Primary Services**: Landscaping, groundwork, construction support\n    - **Typical Projects**: Private outdoor spaces, retaining walls, drainage, machine contractor work\n    - **Geographic Scope**: Ringerike and surrounding municipalities\n    - **Work Pattern**: Project-based with seasonal variations\n\n    ## Employment Characteristics\n    - **Typical Roles**: Anleggsgartner, grunnarbeider, fagarbeider\n    - **Work Schedule**: 37.5 hours/week, typically 07:00-15:00\n    - **Compensation Structure**: Hourly wage (baseline 300 NOK/hour)\n    - **Travel Requirements**: Use of personal vehicles with mileage compensation\n    - **Seasonal Considerations**: Higher activity in construction season\n\n    ## Industry Context\n    - **Sector**: Construction and landscaping services\n    - **Regulatory Environment**: Norwegian labor law, safety regulations\n    - **Professional Requirements**: HMS training, equipment certification\n    - **Market Position**: Growing regional contractor\n```\n\n---\n\n#### `03_legal_framework.md`\n\n```markdown\n    # Legal Framework for Norwegian Employment Contracts\n\n    ## Arbeidsmiljøloven § 14-6 Requirements\n    Mandatory contract elements that must be included:\n\n    ### 1. Party Information\n    - Full names and addresses of employer and employee\n    - Organization number for employer\n    - Personal identification for employee\n\n    ### 2. Employment Details\n    - Job title and description of work\n    - Start date of employment\n    - Duration (if temporary employment)\n    - Justification for temporary employment (if applicable)\n    - Probation period terms (if applicable)\n\n    ### 3. Workplace Information\n    - Primary work location\n    - Indication if work is performed at multiple locations\n    - Travel requirements and compensation\n\n    ### 4. Working Conditions\n    - Weekly working hours\n    - Daily schedule and break arrangements\n    - Overtime compensation terms\n    - Flexibility arrangements\n\n    ### 5. Compensation Structure\n    - Salary amount and payment method\n    - Payment schedule and dates\n    - Additional compensation (overtime, travel, etc.)\n    - Pension and insurance arrangements\n\n    ### 6. Leave and Benefits\n    - Vacation entitlement (5 weeks standard)\n    - Vacation pay percentage (12%)\n    - Sick leave arrangements\n    - Other statutory benefits\n\n    ### 7. Termination Provisions\n    - Notice periods for both parties\n    - Termination procedures\n    - Special provisions for probation period\n\n    ### 8. Additional Terms\n    - Collective agreement status\n    - Confidentiality requirements\n    - Equipment and safety obligations\n    - Regulatory compliance references\n\n    ## Key Legal References\n    - **Arbeidsmiljøloven**: Primary employment law\n    - **Ferieloven**: Vacation and vacation pay regulations\n    - **Folketrygdloven**: Social security and benefits\n    - **Tariffavtaler**: Collective agreements (if applicable)\n```\n\n---\n\n#### `04_content_structure.md`\n\n```markdown\n    # Contract Content Structure\n\n    ## Validated Section Organization\n    Based on legal requirements and practical testing:\n\n    ### Header Section\n    - Contract title\n    - Company information block\n    - Employee information block (with fill-in fields)\n\n    ### 1. Employment Relationship (Ansettelsesforhold)\n    - Start date\n    - Employment type (permanent/temporary)\n    - Duration and justification (if temporary)\n    - Probation period terms\n\n    ### 2. Workplace (Arbeidssted)\n    - Primary location: Ringerike and surrounding areas\n    - Project-based work arrangement\n    - Meeting point coordination\n\n    ### 3. Position and Tasks (Stilling og oppgaver)\n    - Job title field\n    - Core responsibilities: landscaping and groundwork\n    - Additional duties within company scope\n\n    ### 4. Working Hours (Arbeidstid)\n    - Standard: 37.5 hours/week\n    - Schedule: 07:00-15:00 typical\n    - Break arrangements per AML\n    - Flexibility provisions\n\n    ### 5. Salary and Compensation (Lønn og godtgjørelse)\n    - Hourly rate: 300 NOK baseline\n    - Payment date: 5th of each month\n    - Overtime provisions per AML § 10-6\n    - Travel compensation: state rates\n\n    ### 6. Vacation and Vacation Pay (Ferie og feriepenger)\n    - 5 weeks vacation per ferieloven\n    - 12% vacation pay\n    - Payment timing for short-term employment\n\n    ### 7. Termination (Oppsigelse)\n    - Probation period: 14 days\n    - Post-probation: 1 month mutual\n\n    ### 8. Miscellaneous (Diverse)\n    - Equipment provision by employer\n    - Instruction compliance\n    - No collective agreement status\n\n    ### Signature Section\n    - Date and location\n    - Employer signature line\n    - Employee signature line\n```\n\n---\n\n#### `05_design_principles.md`\n\n```markdown\n    # Design Principles for Contract Generation\n\n    ## Content-First Approach\n    - **Markdown Foundation**: Start with structured markdown for content clarity\n    - **Format Flexibility**: Enable multiple output formats (HTML, PDF, DOCX) from single source\n    - **Legal Precision**: Prioritize accuracy and compliance over aesthetic concerns\n    - **Readability**: Ensure contracts are accessible to both legal and non-legal readers\n\n    ## Structural Guidelines\n\n    ### Brevity and Clarity\n    - One A4 page maximum including signatures\n    - Essential information only - eliminate redundancy\n    - Clear section headers and logical flow\n    - Concise language while maintaining legal validity\n\n    ### Visual Organization\n    - **Tables**: Use markdown tables for structured data presentation\n    - **Emphasis**: Bold for critical terms and amounts\n    - **Separation**: Clear visual breaks between sections\n    - **Fill-in Fields**: Consistent formatting for variable content\n\n    ### Modularity\n    - **Reusable Components**: Standardized sections that can be combined\n    - **Variable Content**: Clear separation of fixed vs. customizable elements\n    - **Template Logic**: Support for conditional content (permanent vs. temporary)\n    - **Validation Points**: Built-in checks for required information\n\n    ## Technical Considerations\n\n    ### Markdown Advantages\n    - Version control friendly\n    - Human readable in source form\n    - Multiple rendering options\n    - Easy content iteration and refinement\n    - Separation of content from presentation\n\n    ### Output Format Flexibility\n    - **HTML**: Web preview and browser printing\n    - **PDF**: Official document archival\n    - **DOCX**: Microsoft Word compatibility\n    - **Plain Text**: Fallback and accessibility\n\n    ### User Experience\n    - **Interactive Input**: Guided data collection\n    - **Validation**: Real-time checking of required fields\n    - **Preview**: Show formatted result before finalization\n    - **Export Options**: Multiple format choices based on need\n\n    ## Quality Assurance\n    - **Legal Review**: Regular validation against current law\n    - **Practical Testing**: Real-world usage verification\n    - **Stakeholder Feedback**: Input from HR and legal users\n    - **Continuous Improvement**: Iterative refinement based on usage patterns\n```\n\n---\n\n#### `06_implementation_insights.md`\n\n```markdown\n    # Implementation Insights from Previous Iterations\n\n    ## Evolution Pattern Analysis\n\n    ### v1-v2: Over-Engineering Phase\n    - **Lesson**: Complex PDF generation frameworks created unnecessary complexity\n    - **Issue**: Multiple library dependencies without clear benefit\n    - **Result**: Functional but overly complicated solutions\n\n    ### v3-v4: Content Refinement Phase\n    - **Breakthrough**: Markdown-first approach for content development\n    - **Success**: Clear separation of content from presentation concerns\n    - **Validation**: Legal content structure solidified through iteration\n\n    ### v5: Simplification Success\n    - **Achievement**: Working solution with minimal dependencies\n    - **Technology**: python-docx proved sufficient for DOCX generation\n    - **User Experience**: Interactive CLI provided practical usability\n\n    ## Key Technical Learnings\n\n    ### What Works\n    - **Single Purpose Libraries**: python-docx for DOCX, simple and reliable\n    - **Interactive Prompts**: User-friendly data collection\n    - **Template-Based Generation**: Structured approach to content insertion\n    - **Minimal Dependencies**: Fewer moving parts, more reliable operation\n\n    ### What Doesn't Work\n    - **Complex PDF Engines**: Over-engineered solutions for simple requirements\n    - **Multiple Format Libraries**: Unnecessary complexity for single output need\n    - **Analysis Paralysis**: Extensive reverse-engineering without clear benefit\n    - **Framework Building**: Creating abstractions before understanding requirements\n\n    ## Strategic Insights\n\n    ### Simplicity Principle\n    - Start with the simplest solution that meets requirements\n    - Add complexity only when clearly justified\n    - Prefer proven, stable libraries over cutting-edge alternatives\n    - Focus on user needs rather than technical elegance\n\n    ### Content-First Development\n    - Establish legal content accuracy before technical implementation\n    - Use markdown for rapid content iteration\n    - Separate content structure from output formatting\n    - Validate with stakeholders early and often\n\n    ### User-Centered Design\n    - Interactive mode essential for practical adoption\n    - Clear error messages and guidance\n    - Flexible input handling (optional vs. required fields)\n    - Multiple output options to meet different use cases\n\n    ## Recommended v6 Approach\n\n    ### Phase 1: Content Foundation\n    - Perfect markdown template with all legal requirements\n    - Validate content structure with legal review\n    - Test readability and completeness\n\n    ### Phase 2: Generation Logic\n    - Implement simple template substitution\n    - Add interactive data collection\n    - Create validation and error handling\n\n    ### Phase 3: Output Formats\n    - Start with HTML rendering for preview\n    - Add PDF generation if needed\n    - Consider DOCX for compatibility\n    - Maintain markdown as source of truth\n\n    ### Success Metrics\n    - Legal compliance verified\n    - User adoption in real scenarios\n    - Maintenance simplicity\n    - Output quality and professionalism\n```\n\n---\n\n#### `07_contract_template.md`\n\n```markdown\n    # ARBEIDSAVTALE\n\n    ## Parter\n\n    | **Arbeidsgiver** | **Arbeidstaker** |\n    |------------------|------------------|\n    | **Ringerike Landskap AS** | **Navn:** _________________________ |\n    | **Org.nr:** *********** | **Adresse:** ______________________ |\n    | **Adresse:** Birchs vei 7, 3530 Røyse | **Fødselsdato:** ___________________ |\n\n    ---\n\n    ## 1. Ansettelsesforhold\n\n    | Element | Detaljer |\n    |---------|----------|\n    | **Startdato** | _________________________ |\n    | **Type** | ☐ Fast ☐ Midlertidig t.o.m. _____________ |\n    | **Grunnlag** (hvis midlertidig) | _________________________ |\n    | **Prøvetid** | ☐ Ingen ☐ _____ måneder (maks 6) |\n\n    ---\n\n    ## 2. Arbeidssted\n\n    Ringerike og omegn, oppmøtested etter prosjekt.\n\n    ---\n\n    ## 3. Stilling og oppgaver\n\n    | **Stilling** | _________________________________ |\n    |--------------|-----------------------------------|\n    | **Oppgaver** | Anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten. |\n\n    ---\n\n    ## 4. Arbeidstid\n\n    37,5 t/uke, normalt kl. 07–15. Pauser og fleksibilitet ihht. AML.\n\n    ---\n\n    ## 5. Lønn og godtgjørelse\n\n    | Element | Detaljer |\n    |---------|----------|\n    | **Timesats** | Kr 300,- |\n    | **Utbetaling** | Den 5. hver måned |\n    | **Overtid** | Etter AML § 10-6 |\n    | **Kjøring** | Statens satser |\n\n    ---\n\n    ## 6. Ferie og feriepenger\n\n    5 uker ferie iht. ferieloven. Feriepenger 12%. Utbetales ved fratreden ved kort ansettelse.\n\n    ---\n\n    ## 7. Oppsigelse\n\n    | Periode | Frist |\n    |---------|-------|\n    | **I prøvetid** | 14 dager |\n    | **Etter prøvetid** | 1 måned gjensidig |\n\n    ---\n\n    ## 8. Diverse\n\n    Arbeidstaker følger instrukser, arbeidsgiver leverer arbeidstøy/verktøy. Ingen tariffavtale pr. dato.\n\n    ---\n\n    ## Signatur\n\n    | **Sted/dato:** Røyse, _____________ | |\n    |-----------------------------------|---|\n    | **Arbeidsgiver:** ________________ | **Arbeidstaker:** ________________ |\n\n    ---\n\n    *Arbeidsavtale i henhold til arbeidsmiljøloven. Begge parter beholder kopi.*\n```\n\n# OPPGAVE\n\nVennligst gjør deg kjent med vedlagt informasjon og foreslå en komplett versjon av arbeidskontrakten som er tilpasset Ringerike Landskap As. Kontrakten skal oppfylle minimumskravene i arbeidsmiljøloven § 14-6, samtidig som den i fullstendig versjon kan representeres på **èn** A4-side (inkludert underskrift). Det er viktig at kontrakten både fungerer som lettleselig og oversiktlig når vist som ren tekst, men også at den skrives på en måte som gjør at det ser pent ut som html.\n\nOppsummering av krav:\n- Får plass på én A4-side\n- Oppfyller alle juridiske minstekrav\n- Er spesialtilpasset **Ringerike Landskap AS**\n- Egner seg for både fast og midlertidig (inkl. sesongbasert) ansettelse\n- Pent og strukturert formatering både i tekstfil og når rendret som html"""
