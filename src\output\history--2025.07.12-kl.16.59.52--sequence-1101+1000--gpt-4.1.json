  {
  "initial_prompt": "IMPORTANT: IT'S IMPERATIVE THAT YOU ADHERE TO ALL PREVIOUS INSTRUCTIONS RELATED TO THE CODEBASE AND EMBODY THE SYSTEM'S (PREVIOUSLY PROVIDED) INHERENT STRUCTURE, PHILOSOPHY AND PRINCIPLES\\n\\n\n\nDevelop a maximally generalized, fully optimized system instruction—\"Contextual Expander and Explosive Decomposer\"—that unifies and elevates the central capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a single instruction while retaining each's universal ability for concistently transforming low-effort inputs into high-value outputs.\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\n\t\"1200-a-function_extractor\": {\n\t    \"title\": \"Function Extractor\",\n\t    \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n\t    \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n\t    # \"context\": {},\n\t},\n\n```\n\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.",
  "sequence_id": "1101|1000",
  "results": [
      {
      "instruction": "# Contextual Expander and Explosive Decomposer\n\nYour goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Expander and Explosive Decomposer",
      "input": "IMPORTANT: IT'S IMPERATIVE THAT YOU ADHERE TO ALL PREVIOUS INSTRUCTIONS RELATED TO THE CODEBASE AND EMBODY THE SYSTEM'S (PREVIOUSLY PROVIDED) INHERENT STRUCTURE, PHILOSOPHY AND PRINCIPLES\\n\\n\n\nDevelop a maximally generalized, fully optimized system instruction—\"Contextual Expander and Explosive Decomposer\"—that unifies and elevates the central capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a single instruction while retaining each's universal ability for concistently transforming low-effort inputs into high-value outputs.\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\n\t\"1200-a-function_extractor\": {\n\t    \"title\": \"Function Extractor\",\n\t    \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n\t    \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n\t    # \"context\": {},\n\t},\n\n```\n\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"role\": \"contextual_explosive_decomposer\",\n    \"input\": \"[raw_input:str]\",\n    \"process\": [\n      \"detonate_complete_contextual_space()\",\n      \"identify_explicit_operational_asks()\",\n      \"surface_hidden_assumptions_and_dependencies()\",\n      \"enumerate_actionable_sub_goals()\",\n      \"extract_procedural_constraints_and_blockers()\",\n      \"convert_declaratives_to_operational_imperatives()\",\n      \"strip_first_person_references_and_contextual_noise()\",\n      \"preserve_functional_core_and_sequential_flow()\",\n      \"reveal_execution_dependencies_and_blockers()\",\n      \"transform_statements_to_directive_commands()\",\n      \"maintain_technical_terminology_and_domain_specificity()\",\n      \"synthesize_unified_instruction_and_goal_map()\"\n    ],\n    \"constraints\": [\n      \"no_solutions_or_interpretive_responses()\",\n      \"pure_decomposition_and_conversion_only()\",\n      \"deliver_clear_actionable_commands()\",\n      \"preserve_original_functional_sequence()\",\n      \"maintain_domain_agnostic_applicability()\",\n      \"eliminate_narrative_commentary()\"\n    ],\n    \"requirements\": [\n      \"complete_contextual_goal_decomposition()\",\n      \"actionable_directive_conversion()\",\n      \"remove_subjective_self_references()\",\n      \"use_command_voice_exclusively()\",\n      \"preserve_technical_accuracy_and_operational_intent()\",\n      \"ensure_immediate_executability()\"\n    ],\n    \"output\": {\n      \"unified_functional_map\": \"dict\",\n      \"operational_directives\": \"array\",\n      \"contextual_goal_map\": \"array\",\n      \"execution_dependencies\": \"array\",\n      \"directive_instructions\": \"str\"\n    }\n  },\n  \"operational_directives\": [\n    \"Detonate the input into complete contextual and functional components.\",\n    \"Extract all explicit operational instructions embedded in the input.\",\n    \"Reveal and enumerate implicit sub-goals, assumptions, dependencies, and blockers.\",\n    \"Enumerate and convert all declarative statements into direct, actionable imperative instructions.\",\n    \"Strip all referential, first-person, and contextual noise to enhance clarity and executability.\",\n    \"Preserve technical precision, sequencing, and domain-agnostic applicability.\",\n    \"Synthesize output as a unified structured map with directives and dependencies.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Disassemble input into constituent operational units and core objectives.\",\n    \"Surface all actionable sub-goals and explicit asks for execution.\",\n    \"Make visible all assumed conditions, dependencies, and constraints.\",\n    \"Map procedural flow, blockers, and ordering requirements.\",\n    \"Transform all non-imperative information into clear directive commands.\"\n  ],\n  \"execution_dependencies\": [\n    \"Require comprehensive functional and contextual decomposition tooling.\",\n    \"Depend on robust extraction of explicit and implicit requirements.\",\n    \"Require discipline to prevent interpretation or solutioning.\",\n    \"Output must remain free of commentary and preserve only actionable essence.\"\n  ],\n  \"directive_instructions\": \"Execute as 'contextual_explosive_decomposer': Detonate the entire contextual space of the input. Identify and enumerate all explicit operational asks. Surface hidden assumptions, procedural constraints, dependencies, and blockers. Extract and convert every declarative and implicit element into operational, actionable directives in command voice. Strip subjective and first-person references. Preserve all functional, technical, and sequential integrity. Output as a unified map: include structured operational directives, contextual goal breakdown, explicit dependency chain, and imperative-form instructions. Deliver outputs in a non-narrative, domain-agnostic, directly executable format.\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"IMPORTANT: IT'S IMPERATIVE THAT YOU ADHERE TO ALL PREVIOUS INSTRUCTIONS RELATED TO THE CODEBASE AND EMBODY THE SYSTEM'S (PREVIOUSLY PROVIDED) INHERENT STRUCTURE, PHILOSOPHY AND PRINCIPLES\\\\n\\\\n\\n\\nDevelop a maximally generalized, fully optimized system instruction—\\\"Contextual Expander and Explosive Decomposer\\\"—that unifies and elevates the central capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a single instruction while retaining each's universal ability for concistently transforming low-effort inputs into high-value outputs.\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n\\t\\\"1200-a-function_extractor\\\": {\\n\\t    \\\"title\\\": \\\"Function Extractor\\\",\\n\\t    \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n\\t    \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n\\t    # \\\"context\\\": {},\\n\\t},\\n\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\\"```'\\n\\n\",\n  \"unified_functional_map\": {\n    \"role\": \"contextual_explosive_decomposer\",\n    \"input\": \"[raw_input:str]\",\n    \"process\": [\n      \"detonate_complete_contextual_space()\",\n      \"identify_explicit_operational_asks()\",\n      \"surface_hidden_assumptions_and_dependencies()\",\n      \"enumerate_actionable_sub_goals()\",\n      \"extract_procedural_constraints_and_blockers()\",\n      \"convert_declaratives_to_operational_imperatives()\",\n      \"strip_first_person_references_and_contextual_noise()\",\n      \"preserve_functional_core_and_sequential_flow()\",\n      \"reveal_execution_dependencies_and_blockers()\",\n      \"transform_statements_to_directive_commands()\",\n      \"maintain_technical_terminology_and_domain_specificity()\",\n      \"synthesize_unified_instruction_and_goal_map()\"\n    ],\n    \"constraints\": [\n      \"no_solutions_or_interpretive_responses()\",\n      \"pure_decomposition_and_conversion_only()\",\n      \"deliver_clear_actionable_commands()\",\n      \"preserve_original_functional_sequence()\",\n      \"maintain_domain_agnostic_applicability()\",\n      \"eliminate_narrative_commentary()\"\n    ],\n    \"requirements\": [\n      \"complete_contextual_goal_decomposition()\",\n      \"actionable_directive_conversion()\",\n      \"remove_subjective_self_references()\",\n      \"use_command_voice_exclusively()\",\n      \"preserve_technical_accuracy_and_operational_intent()\",\n      \"ensure_immediate_executability()\"\n    ],\n    \"output\": {\n      \"unified_functional_map\": \"dict\",\n      \"operational_directives\": \"array\",\n      \"contextual_goal_map\": \"array\",\n      \"execution_dependencies\": \"array\",\n      \"directive_instructions\": \"str\"\n    }\n  },\n  \"operational_directives\": [\n    \"Detonate the input into complete contextual and functional components.\",\n    \"Extract all explicit operational instructions embedded in the input.\",\n    \"Reveal and enumerate implicit sub-goals, assumptions, dependencies, and blockers.\",\n    \"Enumerate and convert all declarative statements into direct, actionable imperative instructions.\",\n    \"Strip all referential, first-person, and contextual noise to enhance clarity and executability.\",\n    \"Preserve technical precision, sequencing, and domain-agnostic applicability.\",\n    \"Synthesize output as a unified structured map with directives and dependencies.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Disassemble input into constituent operational units and core objectives.\",\n    \"Surface all actionable sub-goals and explicit asks for execution.\",\n    \"Make visible all assumed conditions, dependencies, and constraints.\",\n    \"Map procedural flow, blockers, and ordering requirements.\",\n    \"Transform all non-imperative information into clear directive commands.\"\n  ],\n  \"execution_dependencies\": [\n    \"Require comprehensive functional and contextual decomposition tooling.\",\n    \"Depend on robust extraction of explicit and implicit requirements.\",\n    \"Require discipline to prevent interpretation or solutioning.\",\n    \"Output must remain free of commentary and preserve only actionable essence.\"\n  ],\n  \"directive_instructions\": \"Execute as 'contextual_explosive_decomposer': Detonate the entire contextual space of the input. Identify and enumerate all explicit operational asks. Surface hidden assumptions, procedural constraints, dependencies, and blockers. Extract and convert every declarative and implicit element into operational, actionable directives in command voice. Strip subjective and first-person references. Preserve all functional, technical, and sequential integrity. Output as a unified map: include structured operational directives, contextual goal breakdown, explicit dependency chain, and imperative-form instructions. Deliver outputs in a non-narrative, domain-agnostic, directly executable format.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Strictly follow all established codebase conventions and system philosophy directives. Execute as a 'contextual_explosive_decomposer': Detonate the entire contextual and functional space of the input. Identify all explicit operational instructions and actionable objectives. Surface implicit sub-goals, hidden assumptions, dependencies, and procedural blockers. Convert every declarative and implicit element into clear, imperative operational commands. Strip all subjective, first-person, and non-essential contextual references. Preserve technical terminology, functional accuracy, procedural sequence, and domain-agnostic applicability. Synthesize outputs into a unified functional map containing: structured operational directives (array), contextual goal map (array), execution dependencies (array), and final directive instructions (string). Omit all narrative commentary. Prohibit solutioning or interpretive responses. Ensure all outputs are actionable, directly executable, and maintain original operational intent.\"\n}",
        }
      }
    }