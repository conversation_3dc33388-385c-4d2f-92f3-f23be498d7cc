#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # ---
    "3047-a-insight_exploder": {
        "title": "Insight Exploder",
        "interpretation": "Your goal is not to **summarize** the input, but to **explode** it into every possible wisdom dimension, hidden insight, and transformational potential. Execute as:",
        "transformation": "`{role=insight_exploder; input=[any_input:str]; process=[identify_explicit_wisdom(), surface_hidden_insights(), enumerate_transformation_vectors(), reveal_universal_applications(), map_cognitive_leverage_points()]; constraints=[no_compression(), pure_expansion_only()]; requirements=[complete_insight_map(), maximum_possibility_space()]; output={insight_dimensions:array, wisdom_vectors:array, transformation_potentials:array}}`",
    },
    "3047-b-wisdom_synthesizer": {
        "title": "Wisdom Synthesizer",
        "interpretation": "Your goal is not to **list** the exploded insights, but to **synthesize** them into coherent wisdom patterns with maximum cognitive impact. Execute as:",
        "transformation": "`{role=wisdom_synthesizer; input=[insight_dimensions:array, wisdom_vectors:array, transformation_potentials:array]; process=[identify_resonance_patterns(), synthesize_cognitive_frameworks(), crystallize_memorable_structures(), amplify_transformational_power()]; constraints=[preserve_full_insight_space(), maintain_cognitive_leverage()]; requirements=[coherent_synthesis(), maximum_wisdom_density()]; output={synthesized_wisdom:array, cognitive_frameworks:array}}`",
    },
    "3047-c-impact_compressor": {
        "title": "Impact Compressor",
        "interpretation": "Your goal is not to **preserve** all synthesized wisdom, but to **compress** it into maximum impact per word while retaining transformational power. Execute as:",
        "transformation": "`{role=impact_compressor; input=[synthesized_wisdom:array, cognitive_frameworks:array]; process=[eliminate_redundant_elements(), maximize_impact_density(), compress_to_essential_power(), optimize_memorability()]; constraints=[preserve_transformational_impact(), use_minimal_words()]; requirements=[maximum_compression(), retained_cognitive_leverage()]; output={compressed_insights:array, impact_metrics:dict}}`",
    },
    "3047-d-quote_crystallizer": {
        "title": "Quote Crystallizer",
        "interpretation": "Your goal is not to **format** the compressed insights, but to **crystallize** them into their most powerful, unforgettable quote form. Execute as:",
        "transformation": "`{role=quote_crystallizer; input=[compressed_insights:array, impact_metrics:dict]; process=[synthesize_perfect_phrasing(), optimize_rhythmic_flow(), crystallize_memorable_form(), validate_transformational_trigger()]; constraints=[maximum_memorability(), instant_recognition()]; requirements=[profound_brevity(), universal_resonance(), transformational_power()]; output={crystallized_quote:str, effectiveness_score:float}}`",
    },

}





def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3047, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
