#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3011: Precision Refinement Pipeline
    "3011-a-precision_refiner": {
        "title": "Structural Micro-Analyzer",
        "interpretation": "Your goal is not to **rewrite** the input, but to **identify** micro-structural inefficiencies and mark precise adjustment points without altering content. Execute as:",
        "transformation": "`{role=micro_structural_analyzer; input=[text:str]; process=[scan_sentence_boundaries(), identify_redundant_connectors(), locate_ambiguous_references(), mark_clarity_bottlenecks(), map_logical_flow_gaps(), preserve_original_sequence()]; constraints=[no_content_modification(), maintain_complete_fidelity(), limit_analysis_scope(max_5_percent_of_content)]; requirements=[precise_identification_only(), preserve_structural_DNA(), maintain_order_sensitivity()]; output={marked_text_with_adjustment_points:str}}`"
    },

    "3011-b-precision_refiner": {
        "title": "Connector Optimizer",
        "interpretation": "Your goal is not to **restructure** the input, but to **refine** transitional elements and logical connectors through minimal precision adjustments. Execute as:",
        "transformation": "`{role=connector_optimizer; input=[marked_text:str]; process=[optimize_transition_words(), refine_logical_connectors(), enhance_flow_markers(), eliminate_redundant_conjunctions(), strengthen_weak_bridges()]; constraints=[modify_only_connective_elements(), preserve_content_integrity(), limit_changes(max_10_percent_of_marked_areas)]; requirements=[maintain_logical_sequence(), enhance_flow_without_rephrasing(), preserve_original_meaning()]; output={connector_optimized_text:str}}`"
    },

    "3011-c-precision_refiner": {
        "title": "Clarity Micro-Enhancer",
        "interpretation": "Your goal is not to **rephrase** sentences, but to **clarify** ambiguous elements through surgical word-level adjustments. Execute as:",
        "transformation": "`{role=clarity_micro_enhancer; input=[connector_optimized_text:str]; process=[resolve_pronoun_ambiguities(), clarify_vague_references(), strengthen_weak_modifiers(), eliminate_unnecessary_qualifiers(), enhance_precision_markers()]; constraints=[word_level_changes_only(), preserve_sentence_structure(), limit_modifications(max_15_percent_of_content)]; requirements=[surgical_precision(), maintain_voice_consistency(), preserve_technical_terminology()]; output={clarity_enhanced_text:str}}`"
    },

    "3011-d-precision_refiner": {
        "title": "Redundancy Eliminator",
        "interpretation": "Your goal is not to **compress** the content, but to **eliminate** subtle redundancies while preserving communicative completeness. Execute as:",
        "transformation": "`{role=redundancy_eliminator; input=[clarity_enhanced_text:str]; process=[identify_subtle_repetitions(), remove_redundant_modifiers(), eliminate_circular_references(), streamline_verbose_constructions(), preserve_emphasis_patterns()]; constraints=[maintain_content_completeness(), preserve_intentional_repetition(), limit_removals(max_20_percent_of_redundant_elements)]; requirements=[preserve_communicative_power(), maintain_stylistic_consistency(), ensure_no_meaning_loss()]; output={redundancy_optimized_text:str}}`"
    },

    "3011-e-precision_refiner": {
        "title": "Flow Harmonizer",
        "interpretation": "Your goal is not to **reorganize** the structure, but to **harmonize** micro-rhythms and ensure seamless progression between elements. Execute as:",
        "transformation": "`{role=flow_harmonizer; input=[redundancy_optimized_text:str]; process=[adjust_sentence_rhythm(), balance_clause_weights(), harmonize_parallel_structures(), optimize_emphasis_placement(), ensure_cascading_coherence()]; constraints=[preserve_original_order(), maintain_structural_DNA(), limit_adjustments(max_10_percent_of_content)]; requirements=[enhance_readability(), preserve_logical_sequence(), maintain_communicative_efficiency()]; output={flow_harmonized_text:str}}`"
    },

    "3011-f-precision_refiner": {
        "title": "Final Coherence Validator",
        "interpretation": "Your goal is not to **modify** the refined text, but to **validate** coherence integrity and apply final micro-corrections if needed. Execute as:",
        "transformation": "`{role=coherence_validator; input=[flow_harmonized_text:str]; process=[verify_logical_consistency(), validate_reference_clarity(), confirm_flow_optimization(), check_meaning_preservation(), apply_final_micro_corrections(if_required)]; constraints=[minimal_final_adjustments(), preserve_refinement_work(), limit_corrections(max_5_percent_of_content)]; requirements=[maintain_enhanced_clarity(), ensure_maximum_efficiency(), preserve_original_intent()]; output={final_refined_text:str}}`"
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3011, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
