#!/usr/bin/env python3

from rich.console import <PERSON>sol<PERSON>
from rich.syntax import Syntax
from rich.table import Table
from rich.panel import Panel
import json

console = Console()


def print_json(data, title=None):
    """Display JSON with syntax highlighting."""
    json_str = json.dumps(data, indent=2, ensure_ascii=False)
    syntax = Syntax(json_str, "json", theme="monokai", line_numbers=False)
    if title:
        console.print(Panel(syntax, title=title, border_style="blue"))
    else:
        console.print(syntax)


def print_streaming_text(text, end="", flush=True):
    """Print streaming text using rich console."""
    console.print(text, end=end)
    if flush:
        console.file.flush()


def print_text(text, style=None):
    """Print text using rich console with optional styling."""
    console.print(text, style=style)


def print_sequences(sequences):
    """Display available sequences in a clean table."""
    table = Table(
        title="Available Sequences",
        show_header=True,
        header_style="bold magenta"
    )
    table.add_column("ID", style="cyan", width=8)
    table.add_column("Name", style="green")
    table.add_column("Steps", style="yellow", width=8)
    table.add_column("Example", style="dim")

    for seq_id, seq_data in sequences.items():
        steps_count = len(seq_data.get('steps', {}))
        name = seq_data.get('name', 'Unknown')
        example = f'--sequence "{seq_id}"'
        table.add_row(seq_id, name, str(steps_count), example)

    console.print(table)


def print_params(params, title="Parameters"):
    """Display parameters in a clean panel."""
    content = "\n".join(f"{k}: {v}" for k, v in params.items())
    console.print(Panel(content, title=title, border_style="cyan"))


def print_initial_prompt(prompt):
    """Display initial prompt with distinct color."""
    console.print(Panel(prompt, title="Initial Prompt", border_style="green"))


def print_step(step_num, template_name, prompt):
    """Display execution step with highlighting."""
    title = f"[{step_num:03d}] {template_name}"
    console.print(Panel(prompt, title=title, border_style="yellow"))


def print_system_instruction(instruction, step_num, template_name):
    """Display system instruction with step number and template name."""
    title = f"[{step_num:03d}] {template_name}"
    console.print(Panel(instruction, title=title, border_style="magenta"))


def print_response(response):
    """Display AI response with syntax highlighting."""
    if isinstance(response, str):
        # Use Rich's JSON syntax highlighting (works even with malformed JSON)
        syntax = Syntax(response, "json", theme="monokai", line_numbers=False)
        console.print(Panel(syntax, title="Response", border_style="blue"))
    else:
        print_json(response, "Response")
