  {
  "initial_prompt": "can you please exclusively apply/leverage/utilize only the most *high-value* components of this system, and then produce the most elegant `interpretation` you can find:",
  "sequence_id": "0000",
  "results": [
      {
      "instruction": "# Contextual Expander and Explosive Decomposer\n\nYour goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Expander and Explosive Decomposer",
      "input": "can you please exclusively apply/leverage/utilize only the most *high-value* components of this system, and then produce the most elegant `interpretation` you can find:",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"contextual_expansion\": \"Request restricts system operations to only the highest-value components, explicitly excluding all others.\",\n    \"core_task\": \"Generate a singular, highly refined interpretation based on the exclusive use of these high-value system components.\",\n    \"selection_criteria\": \"Select and apply only those components classified as 'most high-value' within the system architecture.\",\n    \"output_expectation\": \"Produce one output in the form of an 'elegant interpretation.'\"\n  },\n  \"operational_directives\": [\n    \"Identify all system components.\",\n    \"Classify system components by value ranking.\",\n    \"Isolate components designated as 'most high-value.'\",\n    \"Deactivate or exclude all components not classified as 'most high-value.'\",\n    \"Engage only the isolated 'most high-value' components in the interpretative process.\",\n    \"Synthesize output strictly using engaged components.\",\n    \"Generate and deliver a singular, maximally elegant interpretation based on synthesis.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Optimization of system performance through exclusive selection of high-value elements.\",\n    \"Reduction of process scope to enforce elegance and refinement of the interpretative output.\",\n    \"Operational minimization and focus for maximally effective results.\"\n  ],\n  \"execution_dependencies\": [\n    \"Comprehensive inventory and classification of system components.\",\n    \"Accurate determination of component value hierarchies within system architecture.\",\n    \"Successful isolation and engagement of highest-value components exclusively.\",\n    \"Guaranteed exclusion of all non-essential or lower-value components.\"\n  ],\n  \"directive_instructions\": \"Inventory all system components. Rank each component’s operational value. Select only those classified as most high-value. Deactivate all remaining components. Utilize exclusively the selected high-value components to synthesize a single, elegant interpretation. Output only this refined interpretation.\"\n}",
        }
      }
    }