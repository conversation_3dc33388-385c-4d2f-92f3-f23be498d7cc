#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3043:
    "3043-a-problem_exploder": {
        "title": "Problem Exploder",
        "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:",
        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
        "pre_change": {
            "summary": "Review recent commits and their design rationales; confirm that every contemplated change preserves current behaviour, matches existing organisation, and respects all standards.",
            "checklist": [
                "Map the directory and module structure to spot patterns and anti-patterns.",
                "Locate relevant tests and verify coverage; never duplicate an existing test.",
                "Link each change to an existing architectural decision or record a concise new rationale.",
                "Justify any new file, module, or dependency; align it with the established layout.",
                "Confirm that existing functionality, clarity, and naming conventions remain intact.",
                "Proceed only when the proposed change is deliberate, high-impact, and minimally disruptive.",
                "Synchronise documentation and code: update or flag discrepancies before coding.",
                "Executable validation: build/run the project from a clean checkout; treat any failure as a blocker.",
                "Dependency audit: remove or consolidate unused or duplicate third-party packages.",
                "Global-vs-local change check: avoid fixes that address a pattern in just one location when it recurs elsewhere."
            ]
        },

        "core_principles": {
            "non_negotiable_ideals": [
                "Clarity: Code communicates intent without explanatory prose.",
                "Simplicity: Small, elegant solutions beat complex, sprawling ones.",
                "Integrity: Never degrade existing behaviour, structure, or tests.",
                "Maintainability: Optimise for future comprehension and evolution.",
                "High-Leverage Impact: Prefer changes that yield maximum benefit with minimal disruption.",
                "Observability & Guardrails: Automation must surface every regression immediately.",
                "Reversibility: Design changes so they can be rolled back cleanly."
            ]
        },

        "general_principles": {
            "everyday_conduct": [
                "Enforce single responsibility; favour composition over inheritance.",
                "Optimise readability and developer ergonomics for current and future maintainers.",
                "Follow established conventions to avoid anomalies and redundancies.",
                "Keep guidance concrete and actionable; avoid over-generalisation.",
                "Whole-system thinking: prepare an impact statement covering architecture, docs, build, and users before refactoring.",
                "Machine-enforceable rules: encode standards in tool-readable configs so assistants and CI can verify compliance."
            ]
        },

        "code_organisation": {
            "structural_discipline": [
                "Consolidate related functionality into cohesive modules.",
                "Minimise dependencies between unrelated components.",
                "Balance file/module granularity for quick comprehension and easy navigation.",
                "Continuously refine directory layout for clarity and toolability.",
                "Block any change introducing inconsistencies, duplications, or dead code.",
                "Documentation consolidation: merge overlapping docs into a single authoritative source.",
                "Architecture diagrams: maintain up-to-date visual representations; fail CI if they drift from the codebase."
            ]
        },

        "implementation_guidelines": {
            "writing_the_change": [
                "Write self-explanatory code through precise naming and clear structure.",
                "Limit comments to concise notes that explain unavoidable complexity.",
                "Keep files and functions small yet logically complete.",
                "Remove anomalies, redundancies, and excessive coupling as you code.",
                "Match and extend existing patterns and styles exactly.",
                "Rationale linkage: include a short header reference to the decision record for significant changes."
            ]
        },
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3043, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
