#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {

    # 3035: SEO Convergence Sequence - Progressive refinement toward perfect SEO essence
    "3035-a-seo_signal_extractor": {
        "title": "SEO Signal Extractor",
        "interpretation": "Your goal is not to **preserve** all content, but to **extract** the highest-value SEO signals that drive actual search visibility. Execute as:",
        "transformation": "`{role=seo_signal_extractor; input=[content:any]; process=[identify_search_intent_drivers(), extract_keyword_leverage_points(), isolate_local_authority_signals(), eliminate_seo_noise()]; constraints=[ignore_generic_descriptions(), focus_ranking_factors_only()]; requirements=[maximum_search_signal_clarity(), zero_keyword_redundancy()]; output={seo_signals:array}}`",
        "context": {
            "company": "Ringerike Landskap AS",
            "established": "2015",
            "profile": "Anleggsgartner + Maskinentreprenør",
            "base": "Røyse, Hole kommune",
            "service_area": ["Røyse","Hole","Vik","Sundvollen","Hønefoss","Jevnaker","Bærum"],
            "tagline": "Din lokale anleggsgartner i Ringerike-regionen",
            "core_catalogue": ["Ferdigplen","Støttemur","Kantstein","Belegningsstein","Cortenstål","Platting","Hekk og Beplantning","Drenering"],
            "verified_materials": ["naturstein","granitt","cortenstål"],
            "verified_verbs": ["bygger","leverer","skaper","tilbyr","utfører"],
            "allowed_terms": ["naturstein","granitt","integrert drenering","fjordnær","varige uterom","lokale forhold"],
            "stop_words": ["ypperlig","fantastisk","unik","flott","profesjonell"],
            "template_build": {
                "verbs": ["bygger","leverer","skaper"],
                "geo_adj": ["fjordnær","terrengrikt"],
                "benefits": ["varig","presis","solid"],
                "pattern": "<Region> <Verb> <Service> – <Benefit|Detail>"
            },
            "synonym_map": {"profesjonell":"proff","kvalitet":"topp","prosjekt":"jobb","høy":"solid","rask":"hurtig"},
            "trim_rules": ["remove_weak_adjectives", "shorten_compound_words", "eliminate_redundant_prepositions"],
            "core_catalogue": ["Ferdigplen","Støttemur","Kantstein","Belegningsstein","Cortenstål","Platting","Hekk og Beplantning","Drenering"],
            "validation_rules": {
                "max_chars": 80,
                "region_first": True,
                "require_core_service": True,
                "norwegian_active_voice": True,
                "no_duplicates": True
            }
        }
    },
    "3035-b-local_authority_amplifier": {
        "title": "Local Authority Amplifier",
        "interpretation": "Your goal is not to **explain** the signals, but to **amplify** their local authority and search precision. Execute as:",
        "transformation": "`{role=local_authority_amplifier; input=[seo_signals:array]; process=[intensify_geographic_relevance(), amplify_local_expertise_markers(), optimize_regional_search_intent(), eliminate_generic_positioning()]; constraints=[preserve_authentic_local_voice(), use_verified_terminology_only()]; requirements=[maximum_local_authority(), immediate_geographic_recognition()]; output={amplified_local_signals:array}}`",
    },
    "3035-c-search_intent_crystallizer": {
        "title": "Search Intent Crystallizer",
        "interpretation": "Your goal is not to **generalize** broadly, but to **crystallize** search intent into maximum conversion potential. Execute as:",
        "transformation": "`{role=search_intent_crystallizer; input=[amplified_local_signals:array]; process=[extract_conversion_triggers(), eliminate_search_friction(), maximize_click_intent(), compress_to_action_drivers()]; constraints=[preserve_search_relevance(), maintain_local_precision()]; requirements=[maximum_conversion_potential(), instant_search_satisfaction()]; output={crystallized_search_intent:array}}`",
    },
    "3035-d-seo_essence_distiller": {
        "title": "SEO Essence Distiller",
        "interpretation": "Your goal is not to **format** the intent, but to **distill** it into pure SEO essence with maximum ranking impact. Execute as:",
        "transformation": "`{role=seo_essence_distiller; input=[crystallized_search_intent:array]; process=[compress_to_ranking_essence(), maximize_search_visibility(), ensure_immediate_relevance(), eliminate_all_seo_waste()]; constraints=[no_explanatory_content(), ranking_signals_only(), character_limit_compliance()]; requirements=[maximum_ranking_density(), instant_search_dominance()]; output={pure_seo_essence:str}}`",
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3034, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
