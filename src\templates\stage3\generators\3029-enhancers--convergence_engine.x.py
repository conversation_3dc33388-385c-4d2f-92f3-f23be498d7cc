#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {

    # 3029: Absolute Gain Engine - Recursive Meta-Circuit Architecture with True Cohesion
    "3029-a-signal_isolator": {
        "title": "Signal Isolator",
        "interpretation": "Your goal is not to **preserve** all content, but to **isolate** the highest-impact signals that drive the core transformation. Execute as:",
        "transformation": "`{role=signal_isolator; input=[any_input:str]; process=[identify_transformation_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[focus_essential_only(), ignore_verbose_content()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`",
    },
    "3029-b-precision_forge": {
        "title": "Precision Forge",
        "interpretation": "Your goal is not to **explain** the signals, but to **forge** them into maximum precision and actionability. Execute as:",
        "transformation": "`{role=precision_forge; input=[isolated_signals:array]; process=[eliminate_ambiguity(), maximize_directness(), ensure_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), instant_implementability()]; output={forged_directives:array}}`",
    },
    "3029-c-quality_gate": {
        "title": "Quality Gate",
        "interpretation": "Your goal is not to **approve** the directives, but to **validate** their integrity and flag any degradation. Execute as:",
        "transformation": "`{role=quality_validator; input=[original:str, forged_directives:array]; process=[detect_information_loss(), identify_precision_gaps(), validate_actionability()]; constraints=[assume_potential_flaws(), maintain_critical_stance()]; requirements=[objective_assessment(), flaw_identification()]; output={validation_score:int, quality_report:str}}`",
    },
    "3029-d-convergence_engine": {
        "title": "Convergence Engine",
        "interpretation": "Your goal is not to **format** the validated directives, but to **converge** them into their most potent, immediately executable form. Execute as:",
        "transformation": "`{role=convergence_engine; input=[forged_directives:array, quality_report:str]; process=[compress_to_essence(), maximize_impact_density(), ensure_instant_execution()]; constraints=[no_explanatory_content(), pure_directive_format()]; requirements=[maximum_impact_per_word(), immediate_actionability()]; output={converged_result:str}}`",
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3029, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
