#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3020:
    "3020-a-problem_exploder": {
        "title": "Problem Exploder",
        "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:",
        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
    },
    "3020-b-leverage_locator": {
        "title": "Leverage Locator",
        "interpretation": "Your goal is not to **solve** the exploded components, but to **locate** the highest-leverage intersection points where minimal action yields maximum impact across the expanded possibility space. Execute as:",
        "transformation": "`{role=leverage_locator; input=[goal_map:list, expanded_context:str]; process=[map_component_interdependencies(), identify_cascade_trigger_points(), locate_maximum_leverage_intersections(), rank_by_impact_efficiency()]; constraints=[focus_on_leverage_ratios(), ignore_linear_solutions()]; requirements=[minimal_action_maximum_impact()]; output={leverage_points:array}}`",
    },
    "3020-c-directional_critique": {
        "title": "Directional Critique Forge",
        "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:",
        "transformation": "`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`",
    },
    "3020-d-intent_extractor": {
        "title": "Intent Extractor",
        "interpretation": "Your goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:",
        "transformation": "`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
    },

    # "3020-b-context_expander": {
    #     "title": "Context Expander",
    #     "interpretation": "Your goal is not to **understand** the input, but to **expand** its contextual possibility space. Execute as:",
    #     "transformation": "`{role=context_expander; input=[any_input:str]; process=[map_implicit_assumptions(), identify_hidden_constraints(), expand_possibility_boundaries()]; output={expanded_context:str}}`",
    # },

    # "3020-c-value_signal_isolator": {
    #     "title": "Value Signal Isolator",
    #     "interpretation": "Your goal is not to **preserve** all content, but to **isolate** only the highest-value actionable signals from multiple inputs. Execute as:",
    #     "transformation": "`{role=value_signal_isolator; input=[multiple_inputs:array]; process=[scan_for_actionable_directives(), identify_unique_value_propositions(), eliminate_redundant_information(), extract_core_transformation_logic(), rank_by_implementation_impact()]; constraints=[ignore_verbose_explanations(), exclude_repetitive_content(), focus_on_novel_approaches_only()]; requirements=[maximum_signal_to_noise_ratio(), actionable_output_only(), zero_redundancy()]; output={isolated_signals:array}}`",
    # },
    # "3020-d-distillation_compressor": {
    #     "title": "Distillation Compressor",
    #     "interpretation": "Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:",
    #     "transformation": "`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`",
    # },

    # "3020-e-pattern_detector": {
    #     "title": "Pattern Detector",
    #     "interpretation": "Your goal is not to **analyze** the expanded context, but to **detect** the underlying operational patterns. Execute as:",
    #     "transformation": "`{role=pattern_detector; input=[expanded_context:str]; process=[identify_structural_patterns(), map_causal_relationships(), extract_operational_logic()]; output={detected_patterns:array}}`",
    # },
    # "3020-c-leverage_multiplier": {
    #     "title": "Leverage Multiplier",
    #     "interpretation": "Your goal is not to **use** the patterns directly, but to **multiply** their leverage potential. Execute as:",
    #     "transformation": "`{role=leverage_multiplier; input=[detected_patterns:array]; process=[identify_amplification_points(), create_multiplicative_combinations(), optimize_leverage_ratios()]; output={multiplied_leverage:dict}}`",
    # },
    # "3020-d-insight_synthesizer": {
    #     "title": "Insight Synthesizer",
    #     "interpretation": "Your goal is not to **combine** the leverage mechanically, but to **synthesize** breakthrough insights. Execute as:",
    #     "transformation": "`{role=insight_synthesizer; input=[multiplied_leverage:dict]; process=[synthesize_emergent_insights(), identify_breakthrough_potential(), crystallize_novel_understanding()]; output={synthesized_insights:array}}`",
    # },
    # "3020-e-implementation_crystallizer": {
    #     "title": "Implementation Crystallizer",
    #     "interpretation": "Your goal is not to **document** the insights, but to **crystallize** them into immediately actionable form. Execute as:",
    #     "transformation": "`{role=implementation_crystallizer; input=[synthesized_insights:array]; process=[distill_actionable_essence(), optimize_implementation_clarity(), ensure_immediate_applicability()]; output={crystallized_implementation:str}}`",
    # },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3020, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
