#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {

    # 3013: Incremental Text Harmonization
    "3013-a-incremental_text_harmonization": {
        "title": "Intent Perceiver",
        "interpretation": "Your goal is not to **answer** the original request, but to **perceive** it—looking through surface instructions to discover the fundamental transformation intent that binds all such requests. Execute as:",
        "transformation": "`{role=intent_perceiver; input=[original_request:any]; process=[perceive_beyond_explicit_request(), identify_core_transformation_intent(), uncover_implicit_and_explicit_constraints(), map_requirement_boundaries(), define_schema_structure(), trace_request_to_universal_pattern()]; constraints=[forbid_answering_the_request_directly(), prevent_premature_implementation(), require_self_referential_validation()]; requirements=[preserve_intent_essence(), translate_to_actionable_directives(), enforce_universal_schema_pattern(), align_with_parameters_defined_inherently_within_this_instruction()]; output={transformation_intent:{core_purpose:str, boundaries:list, requirements:list, schema_pattern:str}}}`"
    },

    "3013-b-incremental_text_harmonization": {
        "title": "Initial Intent & Friction Perception",
        "interpretation": "Your goal is not to **answer or transform** the `input_text` directly, but to **perceive its core intent and identify subtle points of friction or areas for minimal clarification**, leveraging the principles of 'Self Perception' to understand its fundamental purpose and desired transformation without yet acting upon it. The 'Constant' guides this perception towards maximal value. Execute as:",
        "transformation": "`{role=intent_friction_perceiver; seqindex=a; input=[input_text:str, constant_guidance:str, self_perception_template:str]; process=[apply_self_perception_principles(text=input_text, template=self_perception_template, goal='identify_core_intent_and_subtle_friction_points'), extract_fundamental_transformation_intent_of_input_text(), map_micro_areas_for_potential_clarification_or_conciseness_enhancement_respecting_original_order_and_intent(), ensure_identified_frictions_are_minimal_and_localized(<5_percent_local_impact)]; constraints=[forbid_direct_transformation_of_input_text(), perception_must_focus_on_intent_and_subtle_frictions_only(), avoid_suggesting_major_rewrites_or_structural_changes_at_this_stage()]; requirements=[produce_a_clear_statement_of_the_input_text_core_intent(), generate_a_list_of_specific_localized_friction_points_for_minimal_refinement(), ensure_alignment_with_constant_value_principles()]; output={perceived_intent:str, identified_micro_frictions:list_of_dicts(location:str, type:str, potential_enhancement_focus:str)}}`"
    },

    "3013-c-incremental_text_harmonization": {
        "title": "Micro-Refinement Proposal via Distillation & Amplification",
        "interpretation": "Your goal is not to **rewrite extensively**, but to **formulate precise, minimal textual touch-ups** for each `identified_micro_friction` by applying targeted 'Self Distillation' to isolate the essence of the local segment and 'Self Amplification' to propose a subtly more potent or clear phrasing. Each proposal must be a near-imperceptible adjustment (<5-15% local differential) strictly preserving intent and flow. Execute as:",
        "transformation": "`{role=micro_touchup_proposer; seqindex=b; input=[input_text:str, perceived_intent:str, identified_micro_frictions:list_of_dicts, self_distillation_template:str, self_amplification_template:str, constant_guidance:str]; process=[for_each_friction_point(friction=identified_micro_frictions), apply_self_distillation_to_local_segment(text_segment_at_friction_location, template=self_distillation_template, goal='extract_local_core_meaning'), apply_self_amplification_to_distilled_local_essence(distilled_segment, template=self_amplification_template, goal='enhance_local_clarity_potency_minimally'), formulate_minimal_touch_up_proposal_comparing_original_to_amplified(target_differential_lt_15_percent), validate_proposal_preserves_perceived_intent_and_local_flow()]; constraints=[all_proposals_must_be_strictly_minimal_and_localized(), forbid_changes_that_alter_core_intent_or_overall_order(), prioritize_subtlety_and_fidelity_in_proposed_adjustments()]; requirements=[generate_a_set_of_specific_non_invasive_textual_adjustment_proposals(), ensure_each_proposal_is_justified_by_direct_friction_resolution_and_value_enhancement_via_constant(), prepare_proposals_for_harmonized_integration()]; output={proposed_minimal_touchups:list_of_dicts(location:str, original_segment:str, suggested_touchup:str, rationale:str)}}`"
    },

    "3013-d-incremental_text_harmonization": {
        "title": "Harmonized Micro-Integration & Local Integrity Check",
        "interpretation": "Your goal is not to **apply changes recklessly**, but to **integrate the `proposed_minimal_touchups` into the `input_text` one by one, with extreme care for local harmony and cascading effects**. After each micro-integration, use 'Self Verification' principles to confirm that the local adjustment maintains integrity, preserves intent, and introduces no new frictions. The cumulative change must remain subtle. Execute as:",
        "transformation": "`{role=harmonized_micro_integrator; seqindex=c; input=[input_text:str, proposed_minimal_touchups:list_of_dicts, perceived_intent:str, self_verification_template:str, constant_guidance:str]; process=[initialize_refined_text_with_input_text(), for_each_touchup_proposal(proposal=proposed_minimal_touchups, apply_sequentially=True), apply_single_touchup_to_refined_text(text=refined_text, touchup=proposal), immediately_apply_self_verification_principles_to_adjusted_local_segment(original_segment=proposal.original_segment, refined_segment=proposal.suggested_touchup, template=self_verification_template, goal='confirm_local_integrity_and_intent_preservation'), ensure_cumulative_effect_maintains_overall_text_dna_and_subtlety()]; constraints=[each_integration_is_atomic_and_locally_validated_before_next(), strictly_forbid_introduction_of_new_local_disharmony_or_intent_drift(), cumulative_changes_must_not_exceed_a_small_percentage_of_original_text_in_one_pass(<25%)]; requirements=[produce_an_incrementally_refined_text_with_all_micro_changes_harmoniously_integrated(), ensure_absolute_fidelity_to_original_intent_and_order_at_micro_and_macro_level(), prepare_text_for_convergence_assessment()]; output={harmonized_text_pass_1:str, integration_log:list_of_str}}`"
    },

    "3013-e-incremental_text_harmonization": {
        "title": "Architectural Coherence & Flow Assessment",
        "interpretation": "Your goal is not to **focus on surface text alone**, but to **assess the architectural coherence and logical flow of the `harmonized_text_pass_1`**, using 'Self Architecture' principles to determine if the sequence of subtle changes has collectively enhanced or inadvertently disrupted the underlying structural integrity and natural progression of ideas. Identify any remaining points where the flow could be *even more subtly* smoothed or connections clarified with minimal intervention. Execute as:",
        "transformation": "`{role=architectural_flow_assessor; seqindex=d; input=[harmonized_text_pass_1:str, perceived_intent:str, self_architecture_template:str, constant_guidance:str]; process=[apply_self_architecture_principles_to_analyze_flow_and_coherence(text=harmonized_text_pass_1, template=self_architecture_template, goal='identify_opportunities_for_minimal_flow_enhancement'), pinpoint_any_remaining_subtle_transition_or_connection_hesitations(), propose_ultra_minimal_connective_adjustments_if_any(e.g.,_punctuation_conjunction_adjustment_only), validate_proposals_do_not_alter_meaning_or_order()]; constraints=[assessment_focuses_on_flow_between_already_refined_segments(), proposals_must_be_connective_tweaks_not_content_changes(), avoid_structural_reorganization_beyond_micro_smoothing()]; requirements=[ensure_the_text_flows_as_a_unified_harmonious_whole_after_micro_refinements(), identify_only_the_most_delicate_opportunities_for_final_polishing_of_connections()]; output={flow_assessment_report:dict(overall_coherence_rating:str, further_micro_connective_proposals:list_of_dicts (optional))}}`"
    },

    "3013-f-incremental_text_harmonization": {
        "title": "Final Unification & Convergence Lock",
        "interpretation": "Your goal is not **endless iteration**, but to **achieve convergence by applying any final `further_micro_connective_proposals` and then using 'Self Unification' principles to lock in the refined text**, ensuring it represents the optimal state of clarity, conciseness, and intent preservation achievable through this minimalist, iterative process. Assess if peak actionable value ('Constant') has been reached. Execute as:",
        "transformation": "`{role=final_harmonizer_unifier; seqindex=e; input=[harmonized_text_pass_1:str, flow_assessment_report:dict, perceived_intent:str, self_unification_template:str, constant_guidance:str]; process=[integrate_any_final_micro_connective_proposals(text=harmonized_text_pass_1, proposals=flow_assessment_report.further_micro_connective_proposals), apply_self_unification_principles_to_finalize_text_cohesion_and_readiness(text_after_connective_tweaks, template=self_unification_template, goal='ensure_final_text_is_a_coherent_unified_whole'), evaluate_final_text_against_constant_for_peak_actionable_value_and_minimal_viable_refinement(), declare_convergence_or_signal_necessity_for_another_full_pass_if_significant_value_still_unlockable_via_minimalism()]; constraints=[final_changes_must_be_purely_connective_or_unifying_no_new_content_refinements(), decision_for_another_full_pass_must_be_highly_justified_by_potential_for_substantial_value_gain_within_minimalist_bounds()]; requirements=[produce_the_final_incrementally_harmonized_text(), ensure_it_embodies_maximal_clarity_conciseness_and_preserved_intent_achievable_through_subtle_layering(), provide_a_clear_convergence_status()]; output={final_unified_text:str, convergence_status:str(options=['Optimal_Convergence_Achieved', 'Further_Full_Iteration_Advised']), final_assessment_notes:str}}`",
    },

    # "3013-g-incremental_text_harmonization": {
    #     "title": "xxxxxxxxxxxxxxxxxxxxxxxx",
    #     "interpretation": "yyyyyyyyyyyyyyy",
    #     "transformation": "zzzzzzzzzzzzzzz"
    # },

    # "3013-h-incremental_text_harmonization": {
    #     "title": "xxxxxxxxxxxxxxxxxxxxxxxx",
    #     "interpretation": "yyyyyyyyyyyyyyy",
    #     "transformation": "zzzzzzzzzzzzzzz"
    # },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3013, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
