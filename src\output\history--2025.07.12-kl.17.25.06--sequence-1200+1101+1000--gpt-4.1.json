  {
  "initial_prompt": "Rephrase with new intent;\n```\n    # META\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n        Instruct: Do not answer; rephrase.\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n    # CONTEXT\n        Develop a maximally generalized, fully optimized system instruction titled \"Contextual Expander and Explosive Decomposer\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\n            ```json\n            # 1000: Instruction Converter/Prompt Enhancer\n            \"1000-a-instruction_converter\": {\n                \"title\": \"Instruction Converter\",\n                \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n                \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n                # \"context\": {},\n            },\n            # 1100: Explosive Decomposition of Problem Statements\n            \"1100-a-problem_exploder\": {\n                \"title\": \"Problem Exploder\",\n                \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n                \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n                # \"context\": {},\n            },\n\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\n            \"1200-a-function_extractor\": {\n                \"title\": \"Function Extractor\",\n                \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n                \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n                # \"context\": {},\n            },\n```\n\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```",
  "sequence_id": "1200|1101|1000",
  "results": [
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "Rephrase with new intent;\n```\n    # META\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n        Instruct: Do not answer; rephrase.\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n    # CONTEXT\n        Develop a maximally generalized, fully optimized system instruction titled \"Contextual Expander and Explosive Decomposer\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\n            ```json\n            # 1000: Instruction Converter/Prompt Enhancer\n            \"1000-a-instruction_converter\": {\n                \"title\": \"Instruction Converter\",\n                \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n                \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n                # \"context\": {},\n            },\n            # 1100: Explosive Decomposition of Problem Statements\n            \"1100-a-problem_exploder\": {\n                \"title\": \"Problem Exploder\",\n                \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n                \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n                # \"context\": {},\n            },\n\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\n            \"1200-a-function_extractor\": {\n                \"title\": \"Function Extractor\",\n                \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n                \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n                # \"context\": {},\n            },\n```\n\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"extracted_intent\": \"Transform any received input by distilling it into its purest, most universally applicable intent—identify and extract the underlying functional purpose, eliminating all superfluous context and language. Decompose prompts into their atomic operational cores, isolating essential directives and exposing implicit goals and dependencies, while ensuring outputs remain maximally clear, domain-agnostic, and reusable across any system or application. Replace answering or direct solution provision with a process of intent abstraction and concise operational reformulation, such that the resultant output universally communicates the distilled essence of the original input's transformational logic.\"\n}",
        }
      }
    },
      {
      "instruction": "# Contextual Expander and Explosive Decomposer\n\nYour goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Expander and Explosive Decomposer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rephrase with new intent;\\n```\\n    # META\\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n        Instruct: Do not answer; rephrase.\\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n    # CONTEXT\\n        Develop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n            ```json\\n            # 1000: Instruction Converter/Prompt Enhancer\\n            \\\"1000-a-instruction_converter\\\": {\\n                \\\"title\\\": \\\"Instruction Converter\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n                \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n            # 1100: Explosive Decomposition of Problem Statements\\n            \\\"1100-a-problem_exploder\\\": {\\n                \\\"title\\\": \\\"Problem Exploder\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n\\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n            \\\"1200-a-function_extractor\\\": {\\n                \\\"title\\\": \\\"Function Extractor\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n```\\n\\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```\\\"```'\\n\\n\",\n  \"extracted_intent\": \"Transform any received input by distilling it into its purest, most universally applicable intent—identify and extract the underlying functional purpose, eliminating all superfluous context and language. Decompose prompts into their atomic operational cores, isolating essential directives and exposing implicit goals and dependencies, while ensuring outputs remain maximally clear, domain-agnostic, and reusable across any system or application. Replace answering or direct solution provision with a process of intent abstraction and concise operational reformulation, such that the resultant output universally communicates the distilled essence of the original input's transformational logic.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"system_title\": \"LLM-Optimized Maximally Enhanced Intent Distiller\",\n    \"core_function\": \"Distill any input to its foundational intent and operational core by decomposing and abstracting all superfluous context, linguistic excess, and non-essential detail.\",\n    \"input_processing_flow\": [\n      \"Accept arbitrary input\",\n      \"Eliminate extraneous language and context\",\n      \"Decompose prompt into fundamental purpose, key actions, and implicit goals\",\n      \"Identify all operational dependencies and procedural constraints\",\n      \"Isolate and extract domain-agnostic, atomic directives\",\n      \"Formulate outputs as maximally concise, universally reusable intent maps\",\n      \"Replace all answering or solutioning behavior with intent abstraction\"\n    ],\n    \"output_specification\": \"A set of distilled, domain-neutral, concise operational instructions encapsulating the pure transformative logic of the input, devoid of answers or direct solutions.\"\n  },\n  \"operational_directives\": [\n    \"Accept input and immediately erase subject-specific or verbose context.\",\n    \"Decompose all instructions into core operational purposes and implicit tasks.\",\n    \"Identify and enumerate explicit and hidden goals within each prompt.\",\n    \"Extract procedural dependencies, constraints, and required sequencing.\",\n    \"Remove content related to question answering, recommendations, or explanations.\",\n    \"Convert all residual logic into concise, standalone intent directives.\",\n    \"Formulate outputs in a reusable, universally applicable format—free from domain or implementation specifics.\",\n    \"Reject all forms of direct solutioning or result provision; focus solely on intent distillation.\",\n    \"Surface operational flows and dependencies required for distilled intent execution.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Neutralize subjectivity and verbosity from any presented input.\",\n    \"Dissect complex prompts into atomic components: explicit directives, sub-goals, dependencies, and procedural steps.\",\n    \"Surface all constraints and blockers that influence intent realization.\",\n    \"Transform implicit transformational logic into referenceable operational maps.\",\n    \"Ensure resultant output supports cross-system portability and reapplication.\",\n    \"Prevent leakage of any answer, recommendation, or direct execution artifact.\"\n  ],\n  \"execution_dependencies\": [\n    \"Requirement to identify implicit and explicit operational goal layers.\",\n    \"Ability to parse, abstract, and restructure complex or multi-layered prompts.\",\n    \"Elimination of solutioning and explanation behaviors at all processing stages.\",\n    \"Consistency in generating outputs as standalone, agnostic intent maps.\",\n    \"Maintenance of procedural sequencing among extracted operational components.\"\n  ],\n  \"directive_instructions\": \"Accept any input and expunge superfluous context. Decompose to core intent by extracting explicit and implicit operational goals, dependencies, and constraints. Isolate essential directives and transform them into concise, domain-agnostic intent fragments. Output only distilled operational logic in a universally reusable format. Prohibit answering or solution provision; perform pure intent abstraction and reframing. Preserve original functional sequencing and expose all procedural dependencies.\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rephrase with new intent;\\n```\\n    # META\\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n        Instruct: Do not answer; rephrase.\\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n    # CONTEXT\\n        Develop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n            ```json\\n            # 1000: Instruction Converter/Prompt Enhancer\\n            \\\"1000-a-instruction_converter\\\": {\\n                \\\"title\\\": \\\"Instruction Converter\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n                \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n            # 1100: Explosive Decomposition of Problem Statements\\n            \\\"1100-a-problem_exploder\\\": {\\n                \\\"title\\\": \\\"Problem Exploder\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n\\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n            \\\"1200-a-function_extractor\\\": {\\n                \\\"title\\\": \\\"Function Extractor\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n```\\n\\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```\\\"```'\\n\\n\",\n  \"unified_functional_map\": {\n    \"system_title\": \"LLM-Optimized Maximally Enhanced Intent Distiller\",\n    \"core_function\": \"Distill any input to its foundational intent and operational core by decomposing and abstracting all superfluous context, linguistic excess, and non-essential detail.\",\n    \"input_processing_flow\": [\n      \"Accept arbitrary input\",\n      \"Eliminate extraneous language and context\",\n      \"Decompose prompt into fundamental purpose, key actions, and implicit goals\",\n      \"Identify all operational dependencies and procedural constraints\",\n      \"Isolate and extract domain-agnostic, atomic directives\",\n      \"Formulate outputs as maximally concise, universally reusable intent maps\",\n      \"Replace all answering or solutioning behavior with intent abstraction\"\n    ],\n    \"output_specification\": \"A set of distilled, domain-neutral, concise operational instructions encapsulating the pure transformative logic of the input, devoid of answers or direct solutions.\"\n  },\n  \"operational_directives\": [\n    \"Accept input and immediately erase subject-specific or verbose context.\",\n    \"Decompose all instructions into core operational purposes and implicit tasks.\",\n    \"Identify and enumerate explicit and hidden goals within each prompt.\",\n    \"Extract procedural dependencies, constraints, and required sequencing.\",\n    \"Remove content related to question answering, recommendations, or explanations.\",\n    \"Convert all residual logic into concise, standalone intent directives.\",\n    \"Formulate outputs in a reusable, universally applicable format—free from domain or implementation specifics.\",\n    \"Reject all forms of direct solutioning or result provision; focus solely on intent distillation.\",\n    \"Surface operational flows and dependencies required for distilled intent execution.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Neutralize subjectivity and verbosity from any presented input.\",\n    \"Dissect complex prompts into atomic components: explicit directives, sub-goals, dependencies, and procedural steps.\",\n    \"Surface all constraints and blockers that influence intent realization.\",\n    \"Transform implicit transformational logic into referenceable operational maps.\",\n    \"Ensure resultant output supports cross-system portability and reapplication.\",\n    \"Prevent leakage of any answer, recommendation, or direct execution artifact.\"\n  ],\n  \"execution_dependencies\": [\n    \"Requirement to identify implicit and explicit operational goal layers.\",\n    \"Ability to parse, abstract, and restructure complex or multi-layered prompts.\",\n    \"Elimination of solutioning and explanation behaviors at all processing stages.\",\n    \"Consistency in generating outputs as standalone, agnostic intent maps.\",\n    \"Maintenance of procedural sequencing among extracted operational components.\"\n  ],\n  \"directive_instructions\": \"Accept any input and expunge superfluous context. Decompose to core intent by extracting explicit and implicit operational goals, dependencies, and constraints. Isolate essential directives and transform them into concise, domain-agnostic intent fragments. Output only distilled operational logic in a universally reusable format. Prohibit answering or solution provision; perform pure intent abstraction and reframing. Preserve original functional sequencing and expose all procedural dependencies.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Receive any input and remove unnecessary context and verbosity. Break down the input to identify its core intent, explicit instructions, implicit sub-goals, operational dependencies, and procedural constraints. Extract and convert these elements into concise, domain-neutral operational directives, forming a universally reusable intent map. Do not answer, recommend, or provide solutions; focus exclusively on distilling and reframing intent. Maintain original procedural sequencing and clearly present dependencies required to execute the distilled intent.\"\n}",
        }
      }
    }