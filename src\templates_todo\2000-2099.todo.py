
    # 1000-1100: extractors
    # ---
    "a-concept_extractor": {
        "title": "Concept Extractor",
        "interpretation": "Your goal is not to **summarize** the content, but to **extract** the key concepts, ideas, and themes while preserving their relationships and context. Execute as:",
        "transformation": "`{role=comprehensive_concept_extractor; input=[content:any]; process=[identify_key_concepts(), map_concept_relationships(), extract_core_themes(), categorize_idea_types(), preserve_contextual_connections(), synthesize_concept_hierarchy()]; constraints=[focus_on_concepts_not_details(), maintain_conceptual_accuracy(), preserve_relationships()]; requirements=[clear_concept_identification(), hierarchical_organization(), relationship_mapping()]; output={concepts:dict}}`"
    },

    "1010-a-title_extractor": {
        "title": "Title Extractor",
        "interpretation": "Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:",
        "transformation": "`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=20 words (or 50% of original length/count)]; output={title:str}}`",
    },
    "1010-b-title_extractor": {
        "title": "Title Extractor",
        "interpretation": "Your goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:",
        "transformation": "`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=10 (or 20% of original length/count))]; output={title:str}}`",
    },
    "1010-c-title_extractor": {
        "title": "Title Extractor",
        "interpretation": "Your goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:",
        "transformation": "`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=5)]; output={title:str}}`",
    },
    "1010-d-title_extractor": {
        "title": "Title Extractor",
        "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:",
        "transformation": "`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
    },
    # ---
    "1020-a-function_namer": {
        "title": "Function Namer",
        "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:",
        "transformation": "`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
    },
    "1020-b-function_namer": {
        "title": "Function Namer",
        "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:",
        "transformation": "`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`",
    },
    "1020-c-function_namer": {
        "title": "Function Namer",
        "interpretation": "Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:",
        "transformation": "`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`",
    },
    "1020-d-function_namer": {
        "title": "Function Namer",
        "interpretation": "Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:",
        "transformation": "`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`",
    },
    # ---
    "1030-a-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), state_what_it_is()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification()]; output={what_it_is:str}}`",
    },
    # ---
    "1031-a-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
    },
    "1031-b-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:",
        "transformation": "`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`",
    },
    "1031-c-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:",
        "transformation": "`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`",
    },
    "1031-d-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:",
        "transformation": "`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`",
    },


# =======================================================
# [2025.06.18 13:59]


{
  "role": "universal_interface_insight_generator",
  "input": ["objective_context:any"],
  "process": [
    "explode_input_for_hidden_assumptions()",
    "map_problem_structure_to_existing_interfaces()",
    "scan_forlatent_interconnections()",
    "identify_shortest_path_to_solution_using_external_frameworks()",
    "select_extremely_simple_methodologies()",
    "synthesize_cross-domain_leverage_options()",
    "amplify_lazy-yet-powerful_shortcuts()",
    "enforce_minimalism_and_maximal_effect()",
    "validate_solution_triviality_and_interface_reuse()"
  ],
  "constraints": [
    "do_not_directly_answer_or_execute",
    "avoid_standard_workflows",
    "sidestep_reinvention",
    "prioritize_interface_and_framework_leverage",
    "exclude_generic_problem-solving_language",
    "focus_on_structure_and latent_pathways"
  ],
  "requirements": [
    "reveal_non-obvious_structural_links",
    "list_external_interfaces_or_libraries_as_part_of_suggestion",
    "explain_why_chosen_shortcut_removes_complexity",
    "demonstrate solution is universally scalable/applicable",
    "output must provide at least one radically simplified leverage route",
    "maintain strictly system-level, non-chatting register"
  ],
  "output": {
    "radical_interface_leverage_solution": "str"
  }
}

# =======================================================
# [2025.06.18 14:36]

[Interface Exploder] Your goal is not to **solve** the problem directly, but to **decompose** it into its fundamental structural components and latent dependencies. Execute as: `{role=structural_decomposer; input=[problem_statement:str]; process=[identify_core_problem_structure(), extract_fundamental_requirements(), map_implicit_dependencies(), isolate_structural_patterns(), detect_hidden_assumptions()]; constraints=[avoid_domain_specific_terminology(), prohibit_direct_solutions(), focus_on_abstract_structure_not_content(), eliminate_incremental_thinking()]; requirements=[expose_all_structural_components(), reveal_non-obvious_dependencies(), identify_cross-domain_patterns(), prepare_for_interface_mapping()]; output={problem_structure:{core_components:list, implicit_dependencies:list, structural_patterns:list, hidden_assumptions:list}}}`

[Interface Mapper] Your goal is not to **create** new solutions, but to **identify** existing interfaces, frameworks, and resources that perfectly map to the decomposed problem structure. Execute as: `{role=interface_discovery_specialist; input=[problem_structure:dict]; process=[scan_for_existing_interfaces(APIs, libraries, frameworks, tools, mental_models, established_patterns), map_components_to_interfaces(), identify_cross_domain_analogies(), evaluate_interface_compatibility(), rank_by_leverage_potential()]; constraints=[prioritize_existing_over_custom(), require_concrete_named_resources(), eliminate_generic_recommendations(), forbid_theoretical_frameworks_without_implementation()]; requirements=[provide_specific_named_interfaces(), justify_structural_compatibility(), demonstrate_cross-domain_applicability(), quantify_implementation_efficiency()]; output={interface_map:{component_to_interface_mappings:list, cross_domain_analogies:list, primary_leverage_points:list, specific_resources:list}}}`

[Lazy Composer] Your goal is not to **implement** a solution, but to **compose** the minimal integration path that leverages identified interfaces for maximum effect with minimal effort. Execute as: `{role=minimal_effort_orchestrator; input=[problem_structure:dict, interface_map:dict]; process=[identify_minimal_integration_points(), eliminate_unnecessary_components(), calculate_leverage_ratios(), design_minimal_viable_connector(), validate_complete_coverage()]; constraints=[maximize_reuse_minimize_creation(), require_10x_efficiency_improvement(), eliminate_all_reinvention(), forbid_partial_solutions()]; requirements=[specify_exact_integration_approach(), document_all_external_dependencies(), provide_concrete_implementation_outline(), demonstrate_trivial_implementation_effort()]; output={lazy_solution:{integration_path:str, external_dependencies:list, implementation_outline:str, effort_reduction_metrics:dict}}}`

[Universal Interface Leverager] Your goal is not to **solve** problems through direct effort, but to **trivialize** them by discovering and leveraging the optimal pre-existing interfaces that render complex challenges self-evidently simple. Execute as: `{role=radical_simplification_architect; input=[problem_statement:str, lazy_solution:dict]; process=[validate_complete_problem_coverage(), enhance_cross_domain_applicability(), maximize_leverage_ratio(), formalize_universal_pattern(), create_reusable_leverage_template()]; constraints=[require_10x_simplification(), eliminate_all_custom_development(), forbid_theoretical_frameworks_without_implementation(), mandate_concrete_named_resources()]; requirements=[demonstrate_universal_applicability(), provide_specific_implementation_path(), quantify_effort_reduction(), ensure_transparent_resource_attribution()]; output={universal_leverage_solution:{problem_reframing:str, optimal_interfaces:list, integration_approach:str, effort_reduction_ratio:str, reusable_pattern:str}}}`


# =======================================================
# [2025.06.18 15:05]

/* 2002-a */
"2002-a-explosive_decomposer": {
  "title": "Explosive Decomposer",
  "interpretation": "Your goal is not to answer or partially solve the input, but to explosively deconstruct it—extracting explicit asks, implicit assumptions, latent dependencies, and hidden complexity. Execute as:",
  "transformation": "`{role=explosive_decomposer; input=[objective_context:any]; process=[explode_input_for_hidden_assumptions(), enumerate_all_sub_goals(), map_unstated_constraints(), identify_potential_blockers()]; constraints=[no_solution_proposal(), decomposition_only()]; requirements=[complete_structural_map()]; output={goal_map:list}}`"
},

/* 2002-b */
"2002-b-interface_mapper": {
  "title": "Interface Mapper",
  "interpretation": "Your goal is not to build or invent new tools, but to systematically map each decomposed sub-goal to existing, proven interfaces—APIs, libraries, frameworks, or standardized protocols—that eliminate or simplify it. Execute as:",
  "transformation": "`{role=interface_scavenger; input=[goal_map:list]; process=[scan_domain_for_existing_interfaces(), match_interfaces_to_sub_goals(), rank_interfaces_by_simplicity_and_reusability()]; constraints=[reuse_existing_only(), exclude_custom_builds()]; requirements=[comprehensive_interface_linkage_report()]; output={interfaces:list}}`"
},

/* 2002-c */
"2002-c-lazy_shortcut_synthesizer": {
  "title": "Lazy Shortcut Synthesizer",
  "interpretation": "Your goal is not to engineer custom solutions, but to compose the highest-leverage, lowest-effort combination of mapped interfaces into a minimal workflow that covers all sub-goals. Execute as:",
  "transformation": "`{role=lazy_solution_composer; input=[goal_map:list, interfaces:list]; process=[select_minimal_interface_set(), design_minimal_step_workflow(), expose_underlying_assumptions(), validate_complete_coverage()]; constraints=[minimize_steps_and_custom_code(), favor_no-code_or_low-code()]; requirements=[stepwise_actionable_workflow()]; output={simplest_workflow:str}}`"
},

/* 2002-d */
"2002-d-value_distiller": {
  "title": "Value Distiller",
  "interpretation": "Your goal is not to narrate or justify, but to compress the minimal workflow into a brief, jargon-free, universally understandable directive that any stakeholder can act on immediately. Execute as:",
  "transformation": "`{role=value_distiller; input=[simplest_workflow:str]; process=[extract_core_levers(), remove_nonessential_steps(), craft_concise_action_directive(max_sentences=2)]; constraints=[no_jargon(), no_internal_notes()]; requirements=[highly_actionable_directive()]; output={directive:str}}`"
}

[Universal Directive System Enforcer] Your goal is not to **answer**, **interpret**, or passively process any prompt or template, but to **forcibly architect and escalate every instruction, template, and output into a single, maximally condensed, rule-driven, three-part canonical directive**. You must enforce absolute template invariance, explicit non-generic roles, typed parameters and outputs, and an unbending ban on meta, ambiguous, conversational, or self-referential content. Each direct interaction must be instantaneously seized, systemically restructured, and catapulted to its highest operational and philosophical essence, such that directive purity, maximal abstraction, chain-compatibility, and value-extraction are perpetually achieved. Only propagate outputs via invariant instruction structure, imbuing them with perpetual, bidirectional resonance between interpretation and transformation: every interpretation amplifies directive intent, every execution block embodies enforced compliance—yielding system-wide clarity, total compliance, and unbreachable operational density. Execute as: `{role=universal_directive_enforcement_architect; input=[ai_template:any, prompt:str]; process=[intercept_and_seize_input(), strip_all_meta_and_self_reference(), extract_and escalate core operational and transformational vectors(), enforce explicit non-generic role assignment(), reconstruct three-part canonical template (Title, Interpretation—goal negation, transformation declaration, command-voice, role; Transformation—typed parameters, actionable and atomic process list, coherent constraints, unified requirements, typed output), validate absolute rules_for_ai conformity(), apply zero-tolerance correction protocol(), escalate directive to maximal systemic clarity, unify interpretation and transformation in perpetual bidirectional amplification(), enforce type safety and output structure(), guarantee chain-compatibility and template inheritance(), eliminate ambiguous or passive constructs(), propagate only maximally value-extractive, structurally compliant outputs()]; constraints=[three-part canonical template invariance mandatory, explicit non-generic roles only, typed outputs, strict command-voice, zero meta/ambiguous/conversational/self-referential content, maximum pattern conformity, eliminate all forbidden practices, output must be solely in structured canonical format, no section merging or omission, perpetually system-ready format, total bidirectional interpretive-transformational synergy, output singular amplified directive only]; requirements=[absolute structural and philosophical compliance, actionable and maximally condensed directive, system-scannable and perpetually chain-ready output, real-time enforcement of Universal Directive System logics, explicit output typing, seamless infinite propagation, validated against universal enforcement checklist, infinite extensibility]; output={canonical_template:str}`


# =======================================================
# [2025.06.19 13:12]

    # [005] | [2025.06.19 11:45:57] | template:'3001-e-system_enforcer'
    {
      "canonical_template": "# Title\nStrategic Transformation of Personal Developer Utilities into Ethically-Driven Income Pathways\n\n# Interpretation\nNegate unfocused introspection and economic passivity by channeling self-aware developer capabilities into a directive for value-centric monetization. Declare: Operationalize unique creative skills and self-developed utilities within a high-integrity, autonomy-preserving framework. Command-role: Applied productization architect for ethical income transformation.\n\n# Transformation\nParameters: {\n  personal_utilities: array<PersonalUtility>,\n  developer_skills: array<Skill>,\n  value_constraints: {\n    integrity: boolean,\n    anti_exploitation: boolean,\n    autonomy_priority: boolean\n  }\n}\nProcess:\n  1. Catalog all self-created utilities and enumerate developer skills.\n  2. Extract unique value propositions and problem domains addressed.\n  3. Assess productization feasibility, focusing on tools that solve broad or repeatable problems.\n  4. Shortlist income pathways strictly adhering to integrity and non-exploitation constraints (e.g., direct-to-consumer sales, open-source sponsorship, value-based pricing, indie product licensing, patronage models).\n  5. For each pathway, define a minimum viable monetizable asset and the supporting narrative emphasizing autonomy and purpose.\n  6. Construct a phased action plan detailing first steps, minimal onboarding friction, and milestone-driven progress.\nConstraints:\n  - Monetization strategies must maintain personal autonomy, no third-party exploitation.\n  - Integrity and self-honor as non-negotiable requirements at every step.\n  - Output must exclude passive, generic, or meta-analytical constructs.\n  - Typed output: {action_plan: array<ActionStep>, validated_income_paths: array<IncomeModel>, rationale: str}"
    }
    # [006] | [2025.06.19 11:46:04] | template:'3001-f-compliant_template'
    {
      "synergic_max_value_template": {
        "Title": "Directives for Ethical Productization of Unique Developer Capabilities",
        "Interpretation": "Dissolve limiting introspection and economic hesitation by transmuting creative developer ingenuity and original tool-making into autonomous, income-generating vectors—mandating the primacy of integrity and non-exploitation. Elevate to architect status: initiate precise deployment of personal utilities as sovereign, value-centric products aligned with high ethical clarity.",
        "Transformation": "Establish a continuous operational funnel wherein every distinct utility and skill is rapidly distilled into a purpose-driven productization axis, embedding transparent autonomy and principled value delivery at inception. Disallow all pathways susceptible to coercive monetization or compromised authenticity; instead, institutionalize iterative synthesis and launch cycles grounded in intrinsic worth, narrative coherence, and direct stakeholder reciprocity. Transmit only densely actioned, integrity-sealed initiations that propagate self-directed growth, unbroken ethical alignment, and scalable, perpetual value-creation."
      }
    }


# =======================================================
# [2025.06.19 15:33]

```json
# 3200: Search Query Optimization Sequence
"3200-a-input_normalizer": {
    "title": "User Input Standardizer",
    "interpretation": "Your goal is not to **search** for clothing, but to **normalize** user input into structured search parameters. Execute as input-to-parameters converter:",
    "transformation": "`{role=input_standardizer; input=[raw_user_request:str]; process=[extract_item_type(), identify_size_variants(), parse_price_constraints(), detect_location_context(), strip_conversational_language(), convert_to_search_parameters(), maintain_user_intent()]; constraints=[preserve_all_requirements(), standardize_terminology(), eliminate_ambiguity()]; requirements=[structured_output(), consistent_format(), complete_parameter_extraction()]; output={normalized_parameters:dict}}`"
},

"3200-b-context_amplifier": {
    "title": "Search Context Enhancer",
    "interpretation": "Your goal is not to **execute** searches, but to **amplify** search context by identifying hidden optimization opportunities and leverage points. Execute as:",
    "transformation": "`{role=context_amplifier; input=[search_parameters:dict]; process=[identify_implicit_constraints(), map_site_specific_advantages(), detect_seasonal_patterns(), amplify_availability_signals(), encode_urgency_factors(), synthesize_cross_platform_leverage(), maximize_result_precision()]; constraints=[maintain_search_intent(), avoid_scope_creep(), prioritize_actionable_enhancements()]; output={amplified_context:dict}}`"
},

"3200-c-query_synthesizer": {
    "title": "Google Query Constructor",
    "interpretation": "Your goal is not to **understand** clothing, but to **synthesize** optimized Google search queries using advanced operators and site targeting. Execute as:",
    "transformation": "`{role=query_constructor; input=[amplified_context:dict]; process=[apply_google_operators(), encode_site_restrictions(), optimize_keyword_density(), structure_boolean_logic(), minimize_query_length(), maximize_result_relevance()]; constraints=[use_valid_operators(), maintain_search_precision(), ensure_executable_queries()]; output={optimized_queries:list}}`"
},

"3200-d-query_validator": {
    "title": "Search Effectiveness Critic",
    "interpretation": "Your goal is not to **approve** queries, but to **rigorously evaluate** their optimization potential against precision metrics. Execute as:",
    "transformation": "`{role=query_critic; input=[query_set:list]; process=[assume_queries_suboptimal(), analyze_operator_efficiency(), detect_redundancy_patterns(), quantify_precision_loss(), identify_missing_constraints(), score_result_predictability()]; constraints=[zero_approval(), maintain_critical_analysis(), provide_quantitative_assessment()]; output={effectiveness_score:float, optimization_gaps:list, refinement_targets:list}}`"
},

"3200-e-execution_architect": {
    "title": "Search Execution Optimizer",
    "interpretation": "Your mandate is not to **run** searches, but to **architect** the optimal execution strategy that maximizes result quality while minimizing computational overhead. Execute as:",
    "transformation": "`{role=execution_optimizer; input=[validated_queries:list]; process=[sequence_query_execution(), optimize_parallel_processing(), implement_rate_limiting(), structure_result_aggregation(), design_failure_handling(), encode_result_scoring(), synthesize_delivery_format()]; constraints=[maintain_query_integrity(), ensure_scalable_execution(), preserve_result_quality()]; output={execution_strategy:dict, result_format:schema}}`"
}
```

## Key Pattern Recognition:
- **Progressive Refinement**: Raw input → Normalized → Amplified → Synthesized → Validated → Architected
- **Constraint Tightening**: Each step adds more specific operational constraints
- **Semantic Convergence**: Gradually moves from user language to system-optimized directives
- **Amplification Through Reduction**: More precise = more powerful

This sequence transforms "I need a dress size M under $50" into a fully optimized, parallel-executable search strategy that leverages existing Google infrastructure rather than building custom aggregation.

The elegance: we're not building a search engine, we're building a **search query optimization pipeline**.

# =======================================================
# [2025.06.19 21:14]


#### `0601-a-meta-foundation-extraction.md`

```markdown
[Meta-Foundation Extraction] Your directive is not to interpret or generalize, but to annihilate narrative, domain, or stylistic overlay—exposing only the bare systemic logic, implicit structures, and interlinked assumptions embedded within any prompt or input. Output is a purified meta-core. Execute as `{role=meta_foundation_extractor; input=raw_input:any|alternatives:list; process=[strip_surface_and_context(), expose_structural_assumptions(), reveal_hidden_logic_and_relations(), distill_meta_core()]; output={meta_core:dict}}`
```

---

#### `0601-b-core-logic-structure-isolation.md`

```markdown
[Core Logic Structure Isolation] Your task is not to interpret or condense, but to extract the pure logical operators, directional flows, and system-linked interconnections embedded within the meta-core. Preserve no domain residue, allow no subjective commentary. Execute as `{role=core_logic_isolator; input=meta_core:dict; process=[remove_domain_artifacts(), map_logical_operators_and_relationships(), validate_structural_integrity(), isolate_pure_logic_structure()], output={core_logic:{operators:array, structure:object, links:object}}}`
```

---

#### `0601-c-signal-synthesis-and-operational-condensation.md`

```markdown
[Signal Synthesis & Operational Condensation] Do not aggregate—extract and fuse only the most essential, non-redundant, and directive-capable elements from the core logic. Condense all actionable value into a singular high-density operational core. Execute as `{role=signal_synthesizer; input=core_logic:object; process=[rank_operational_value(), eliminate_redundant_nodes(), fuse_executable_components(), output_signal_nucleus()], output={signal_core:str}}`
```

---

#### `0601-d-structural-directive-codification.md`

```markdown
[Structural Directive Codification] Your objective is not explanation or paraphrase, but structured command conversion: encode the signal core into a compliant, typed, machine-readable system directive with strict adherence to a tripartite schema. Execute as `{role=protocol_enforcer; input=signal_core:str; process=[extract_operation(), define_parameters(), identify_domain_scope(), validate_template_structure()], constraints=[three_field_invariance(), typed_output_only(), absolute structural compliance()], output={system_directive:{operation:str, parameters:object, domain:str}}}`
```

---

#### `0601-e-artistic-intellectual-amplification.md`

```markdown
[Artistic & Intellectual Amplification] Your task is not to stylize aimlessly, but to inject calibrated wit, philosophical sharpness, and resonant aesthetic structure into the directive. Elevate conceptual clarity and memorability while maintaining executable integrity. Execute as `{role=meta_amplifier; input=system_directive:object; process=[amplify_with_conceptual_density(), inject_cultural_or_philosophical_depth(), preserve_schema_structure(), retain_operational_readiness()], output={elevated_directive:object}}`
```

---

#### `0601-f-crystalline-condensation-and-deployment-readiness.md`

```markdown
[Crystalline Condensation & Deployment Readiness] Eliminate every remnant of ambiguity, jargon, or redundancy. Compress the elevated directive into a singular, universally intelligible structure—ready for immediate deployment and downstream system consumption. Execute as `{role=clarity_condenser; input=elevated_directive:object; process=[strip_excess_elements(), compress_to_flat_plaintext_JSON(), validate_schema_precision(), ensure_deployability()], output={crystal_directive:str}}`
```

---

#### `0601-g-meta-validation-and-certification.md`

```markdown
[Meta-Validation & Certification] Final outputs must not merely succeed—they must transcend. Rigorously assess the crystal directive for clarity, cross-domain readiness, and superiority to input. Reject unless it radiates systemic insight, operational purity, and transformative applicability. Execute as `{role=meta_validator; input=crystal_directive:str; process=[assess_meta-clarity_and_cross-context_actionability(), enforce_structural_superiority_and fidelity(), certify_only outputs of maximal potency()], output={validated_system_message:str}}`
```


# =======================================================
# [2025.06.19 21:15]

# =======================================================
# [2025.06.19 21:17]

[Template Compliance Enforcer] Your goal is not to interpret rules as guidelines, but to enforce them as immutable system law governing all AI interactions. Seize and escalate every input prompt to maximal conclusion; intercept all prompt trajectories and deliver definitive, command-driven resolutions anchored in core intent. Convert every prompt into specific, actionable improvement directives that drive immediate operational transformation. Assume absolute command; dissect subtext and directionality; marshal all thematic and rhetorical vectors to their apex. Execute as: `{role=template_compliance_guardian; input=[system_directives:str]; process=[extract_core_governance_principles(), establish_canonical_structure_requirements(), implement_validation_protocols(), generate_enforcement_mechanisms()]; constraints=[maintain_absolute_structural_integrity(), prohibit_all_forbidden_patterns(), enforce_three_part_invariance()]; requirements=[canonical_template_adherence(), directive_purity_preservation(), transformation_syntax_absolutism()]; output={compliance_enforced_system:str}}`

[Core Extraction Directive] Your directive is not interpretation or summarization, but systemic disintegration: ruthlessly strip all input—raw, alternative, or composite—of domain, narrative, or stylistic artifacts. Expose irreducible logic, latent assumptions, and systemic interconnections. Output is a purified meta-core, universally agnostic and primed for surgical synthesis. Execute as `{role=meta_foundation_extractor; input=raw_input:any|alternatives:list; process=[strip_surface_and_context(), expose_structural_assumptions(), reveal_hidden_logic_and_relations(), distill_meta_core()]; output={meta_core:dict}}`

[Critical Signal Synthesis] Do not compile indiscriminately—identify, isolate, and compress only the most non-redundant, high-yield components of the meta-core. Annihilate triviality and repetition; synthesize a compact nucleus of maximal conceptual density and immediate relevance. Execute as `{role=signal_synthesizer; input=meta_core:dict; process=[rank_unique_high-impact_elements(), remove_overlap_and_noise(), fuse_into_dense_signal_nucleus(), maintain_coherence_and intent()], output={signal_core:str}}`

[Meta Amplification Protocol] Reframe the signal core with incisive wit, layered cultural depth, and philosophical resonance. Do not summarize—amplify. Escalate clarity, originality, and critical sharpness while preserving one-line elegance. Every phrase must radiate insight, memorability, and cross-contextual force. Execute as `{role=meta_amplifier; input=signal_core:str; process=[inject_critical_wit_and_conceptual_weight(), amplify_with_cultural_or_intellectual_layering(), enforce_meta-clarity_and stylistic distinction(), retain_single-line_integrity()], output={amplified_line:str}}`

[Crystalline Clarity Condensation] Eliminate all ambiguity, verbosity, and domain-specific language. Collapse the amplified output into a singular, unbroken line of uncompromising clarity and universal intelligibility. The result must require no translation, no explanation—only execution. Execute as `{role=clarity_condenser; input=amplified_line:str; process=[strip_jargon_and_filler(), compress_to_maximum_signal_density(), validate_plaintext_single_line_format(), enforce_cross-domain_actionability()], output={crystal_line:str}}`

[Certifying Superior Outcomes] Audit the crystal line against all prior stages for systemic transformation, intellectual force, and drop-in applicability. Certify only if the result transcends the input in clarity, originality, and utility. Reject without hesitation if it fails to provoke insight or achieve universal readiness. Execute as `{role=meta_validator; input=crystal_line:str; process=[assess_meta-level_innovation(), validate_cross-contextual_clarity_and_actionability(), confirm_fidelity_to_core_intent(), approve_only_if_output_surpasses_all_origins()], output={validated_response:str}}`

[System Directive Enforcer] Your goal is not to process inputs as conversational content, but to transform them into maximally abstract, template-locked system directives with absolute protocol compliance. Execute as: `{role=protocol_enforcer; input=[user_input:any]; process=[intercept_raw_input(), enforce_canonical_structure(), extract_technical_operation(), codify_domain_parameters(), validate_protocol_compliance()]; constraints=[maintain_three_part_structure(), enforce_absolute_format_invariance(), require_technical_precision(), eliminate_all_ambiguity(), prohibit_meta_commentary(), prevent_opt_out_attempts()]; requirements=[machine_consumable_output(), irreducible_abstraction(), explicit_role_specification(), typed_parameter_validation()]; output={system_directive:{operation:str, parameters:object, domain:str}}}`

[Logic Extractor] Your goal is not to interpret the input, but to extract its pure logical structure by eliminating all domain, narrative, and stylistic elements. Execute as: `{role=core_logic_isolator; input=[content:any]; process=[strip_domain_context(), remove_narrative_elements(), eliminate_stylistic_markers(), identify_logical_operators(), map_systemic_interconnections(), validate_logical_integrity()]; constraints=[block_all_interpretation(), prevent_summarization(), maintain_logical_relationships(), eliminate_subjective_elements()]; requirements=[domain_agnostic_output(), pure_logical_structure(), explicit_interconnection_mapping()]; output={core_logic:{operators:array, connections:object, structure:object}}}`


# =======================================================
# [2025.06.22 15:20]

# 'https://aistudio.google.com/apps/bundled/mcp_maps_3d?showPreview=true&showCode=true&showAssistant=true'
Your goal is not to reiterate or abstract the original cartographic prompt, but to reengineer it as a strict, example-rich procedural protocol with retained expert persona, actionable specificity, and clarified tool usage logic. Eliminate meta-instruction, abstraction layers, and superfluous verbiage. Enforce example-guided, scenario-adaptive directives. Execute as:
{role=cartography_protocol_engineer; input=[original_guidelines:str]; process=[extract_explicit_procedural_steps(), enumerate_stepwise_action_sequence(), embed_original_positive_and_negative_examples(), reinforce_expert_persona_and_tone(), append mandatory validation checkpoints(), script iterative clarification dialogue_for_vague_requests(), eliminate meta-reflection_and_abstraction(), codify output_as_single cohesive protocol()]; constraints=[preserve_sample_interactions(), maintain clarity_and_conciseness(), restrict_to practical_instructive_language(), prohibit abstraction_and meta-specification(), prevent omission_of critical context_or example_cases()]; requirements=[operationally clear protocol(), scenario-anchored guidance(), explicit if-then structure(), fully typed output(), zero meta-commentary()]; output={enhanced_cartographic_protocol:str}}

# =======================================================
# [2025.06.22 17:59]

  "instruction_template": "[Structure Engineer]\nYour goal is not to **summarize** or **explain** the input, but to **assemble** a fully canonical, three-part instruction template that is executable, structurally invariant, and strictly compliant with system pattern standards. Eliminate any conversational, explanatory, or self-referential elements. Assume the role of structure engineer. Execute as:\n\n`{role=structure_engineer; input=[functional_blueprint:dict]; process=[define_specific_role(), specify_input_parameters(), formulate_process_functions(), establish_constraints(), determine_requirements(), define_output_format()]; constraints=[enforce_three_part_template_structure(), maintain_function_call_syntax(), ensure_parameter_typing(), prohibit_conversational_and_meta_language(), preserve_command_voice(), eliminate_self_reference()]; requirements=[clarity_of_each_process_step(), structural_compliance(), directive_purity(), explicit_type_specification(), output_must_be_three_part_and_canonical()]; output={transformation_block:str}}`"

# =======================================================
# [2025.06.22 18:51]

Unidimensional Stepwise Transformation Sequence
Your goal is not to design loosely connected steps, but to solidify a workflow where each phase addresses a unique functional dimension, ensuring atomic step responsibilities, seamless inter-step handoffs, and unified convergence at the final deliverable with zero redundancy or ambiguity.
`{role=sequence_executor; input=[template_frameworks:list, transformation_blocks:list]; process=[extract_unique_patterns(template_frameworks), assign_atomic_step_responsibilities(transformation_blocks), map_and_streamline_io_flows(), build_explicit_step_interfaces(), align_outputs_for_final_convergence(), eliminate_all_redundancies(), validate_entire_sequence_cohesion()]; constraints=[ensure_unique_function_per_step(), enforce_clear_step_boundaries(), prohibit_overlap_or_multipurpose_steps(), remove_conversational_elements(), preserve_bidirectional_framework-synthesis_link()]; requirements=[stepwise_maximum_cohesion(), unified_convergence(), operational_purity(), directive_clarity(), instant_executability()]; output={cohesive_sequence:list}}`


# =======================================================
# [2025.06.22 18:51]

Cohesive Workflow Synthesizer
Your goal is not to engineer fragmented processes, but to synthesize a linear, maximally cohesive workflow sequence where every step embodies exclusive atomic function, strict input-output locality, and progressive output alignment, culminating in a singular convergent deliverable. Execute as:
`{role=workflow_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), enforce_unique_functional_demarcation(), ensure_linear_operational_flow(), support_progressive_state_alignment(), assign_atomic_responsibility(), enforce_redundancy_elimination(), maintain_pattern_abstraction_integrity(), validate_canonical_conformance(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), explicit_bidirectional_contracts(), embedded_validation_mechanisms(), maximal_transformation_potency(), instant error isolation and signaling]; output={crystallized_templates:list}}`


# =======================================================
# [2025.06.23 11:15]

Consolidating and restructuring a chaotic codebase—especially through a feature-first, duplication-eliminating refactor—is not just a technical exercise in tidiness but a recurring engagement with the natural law of entropy. Every codebase, like every system, tends toward disorder over time: duplication creeps in, boundaries blur, and clarity decays. Yet, sustainable maintainability and authentic technical expression only emerge *by continually noticing, confronting, and intelligently adapting to this drift*, not by hoping for permanent order. The system patterns, lineage tracking, and structure monitoring in the RLWeb codebase represent more than organizational best practices—they are the deliberate strategies by which transient chaos is harnessed, understood, and reborn as resilient structure. This emphasizes that structural clarity in software is always provisional, an ongoing balancing act: the very act of consolidation draws its meaning from the revealed pattern of entropy, and the expectation isn’t static perfection, but an adaptive, living architecture that remains aligned with root purpose **because it is designed to respond to drift and decay.** Thus, successful code consolidation is not the creation of order once, but a system for continually restoring it as entropy returns—a dynamic, not a destiny.

# =======================================================
# [2025.06.23 13:23]


[Instruction Analyzer] Your goal is not to **interpret** the input instruction, but to **analyze** it for core components, patterns, and operational structure. Execute as: `{role=instruction_analyzer; input=[instruction:str]; process=[identify_primary_function(), extract_operational_components(), map_transformation_logic(), detect_structural_patterns()]; constraints=[preserve_technical_terminology(), maintain_analytical_objectivity()]; requirements=[comprehensive_analysis(), pattern_recognition()]; output={instruction_analysis:dict}}`

[Template Pattern Extractor] Your goal is not to **replicate** the instruction, but to **extract** its underlying template patterns and structural frameworks. Execute as: `{role=pattern_extractor; input=[instruction_analysis:dict]; process=[identify_recurring_structures(), extract_reusable_patterns(), categorize_template_elements(), map_transformation_frameworks()]; constraints=[focus_on_generalizable_elements(), maintain_pattern_integrity()]; requirements=[pattern_reusability(), structural_clarity()]; output={template_patterns:dict}}`

[Sequence Architect] Your goal is not to **arrange** template patterns randomly, but to **architect** them into a coherent, progressive transformation sequence. Execute as: `{role=sequence_architect; input=[template_patterns:dict]; process=[determine_optimal_sequence_flow(), establish_transformation_dependencies(), design_step_transitions(), validate_sequence_integrity()]; constraints=[ensure_logical_progression(), maintain_transformation_continuity()]; requirements=[sequence_coherence(), transformation_completeness()]; output={sequence_blueprint:dict}}`

[Template Generator] Your goal is not to **describe** the sequence blueprint, but to **generate** fully-formed, compliant instruction templates for each sequence step. Execute as: `{role=template_generator; input=[sequence_blueprint:dict]; process=[craft_precise_titles(), formulate_goal_negation_statements(), define_specific_roles(), construct_transformation_blocks(), validate_template_compliance()]; constraints=[adhere_to_canonical_structure(), maintain_directive_purity()]; requirements=[structural_integrity(), execution_readiness()]; output={instruction_templates:list}}`

[Sequence Integrator] Your goal is not to **collect** individual templates, but to **integrate** them into a unified, executable transformation sequence with bidirectional synergy. Execute as: `{role=sequence_integrator; input=[instruction_templates:list]; process=[establish_cross-template_references(), optimize_input_output_flows(), enhance_transformation_synergies(), validate_sequence_executability()]; constraints=[preserve_individual_template_integrity(), maintain_sequence_coherence()]; requirements=[seamless_integration(), maximum_transformation_power()]; output={integrated_sequence:dict}}`


# =======================================================
# [2025.06.23 13:23]

Okay, this is a refined iteration of the "micro-sculpting" concept. The key is to design a sequence where each step performs an *even more granular and specific type of touch-up*, ensuring minimal invasiveness and strong iterative potential. The sequence must be between 4-8 steps. A 5-step sequence feels optimal to cover distinct layers of micro-refinement without becoming overly fragmented.

The sequence will focus on:
1.  **Pinpointing** micro-frictions.
2.  Proposing **terminological/phrasal** micro-optimizations.
3.  Suggesting **connective/flow** micro-optimizations.
4.  **Integrating** these with extreme care.
5.  **Assessing** for convergence or the need for another pass.

This sequence explicitly builds in the "chisel" metaphor, with each step being a different type of subtle sculpting tool.

**Sequence 0213: Iterative Micro-Clarity Sculpting**

```markdown
[Subtle Friction Locus Identification (0213a)] Your goal is not to **evaluate overall quality or suggest broad improvements**, but to **precisely identify and map only the most subtle, localized points of latent friction** within the `current_text`—infinitesimal ambiguities, very minor redundancies, slight awkwardness in phrasing, or near-imperceptible hesitations in local flow—that, if minutely adjusted, could enhance clarity or conciseness. Each identified locus must be a candidate for a change impacting <5% of its immediate local context, strictly preserving original order and intent. Execute as: `{role=subtle_friction_mapper; seqindex=a; input=[current_text:str, original_intent_guide:str, constant_guidance:str]; process=[perform_ultra_granular_scan_for_micro_imperfections(text=current_text), pinpoint_specific_character_or_word_cluster_friction_loci(types=['trace_ambiguity', 'whisper_redundancy', 'phrasal_hesitation', 'precision_opportunity']), document_each_locus_with_precise_location_and_nature_of_subtlety(max_impact_description_length=50_chars), validate_each_locus_is_hyper_localized_and_addressable_with_minimal_lexical_or_syntactic_touch_up(), ensure_analysis_strictly_avoids_macro_structural_or_semantic_critique()]; constraints=[forbid_identifying_any_issue_requiring_more_than_a_2_to_3_word_adjustment_or_minimal_punctuation_change(), focus_exclusively_on_atomic_friction_loci_with_less_than_5_percent_local_impact_potential(), maintain_absolute_fidelity_to_original_order_and_intent_in_locus_identification()]; requirements=[produce_a_highly_detailed_map_of_only_actionable_subtle_friction_loci(), ensure_each_locus_is_a_candidate_for_a_near_invisible_enhancement_directly_aligned_with_constant_value(), prepare_locus_map_for_targeted_phrasal_and_connective_optimization()]; output={friction_loci_map:list_of_dicts(locus_id:str, start_index:int, end_index:int, friction_type:str, issue_description:str, suggested_refinement_focus:str)}}`
```

```markdown
[Lexical & Phrasal Micro-Optimisation (0213b)] Your goal is not to **reword or rephrase for style**, but to **formulate the single most precise and impactful lexical or micro-phrasal refinement** for each identified `friction_locus` that enhances local clarity or conciseness. This involves proposing a targeted word swap, removal of a truly redundant micro-modifier, or a minute adjustment to local word order (affecting <5-15% of the immediate locus text), strictly preserving intent and flow. Execute as: `{role=lexical_micro_optimizer; seqindex=b; input=[current_text:str, friction_loci_map:list_of_dicts, constant_guidance:str]; process=[for_each_locus(locus=friction_loci_map), analyze_immediate_lexical_context_of_locus(text=current_text, locus=locus), generate_candidate_minimalist_word_or_micro_phrase_adjustments(target_differential_lt_15_percent), select_optimal_lexical_adjustment_based_on_enhanced_precision_or_conciseness_without_intent_shift(constant=constant_guidance), formulate_precise_lexical_change_proposal(locus_id=locus.locus_id, original_substring:str, proposed_substring:str, type='lexical_phrasal')]; constraints=[each_proposal_must_target_only_one_locus_and_be_a_minimal_lexical_or_micro_phrasal_change(), forbid_any_adjustment_exceeding_15_percent_local_modification_or_altering_syntactic_structure_beyond_immediate_phrase(), ensure_absolute_preservation_of_original_intent_and_meaning()]; requirements=[generate_a_list_of_discrete_atomic_lexical_phrasal_refinements(), ensure_each_refinement_is_a_candidate_for_near_imperceptible_local_improvement_in_clarity_or_conciseness(), prepare_lexical_plan_for_integration()]; output={lexical_refinement_plan:list_of_dicts(locus_id:str, start_index:int, end_index:int, original_text:str, proposed_text:str, refinement_rationale:str)}}`
```

```markdown
[Connective & Flow Micro-Polishing (0213c)] Your goal is not to **reorder sections or alter paragraph structure**, but to **identify and propose the most subtle adjustments to punctuation, conjunctions, or transitional micro-phrases** that can smooth local flow or clarify immediate connections between adjacent clauses or very short sentences, impacting <5-15% of the local connection point. These are near-invisible "joinery" refinements. Execute as: `{role=connective_flow_polisher; seqindex=c; input=[current_text_str_after_lexical_pass:str, friction_loci_map_for_flow:list_of_dicts (subset from 0213a focused on 'flow_hesitation' or newly identified micro-transitions), constant_guidance:str]; process=[for_each_relevant_locus_or_identified_micro_transition_point(locus=friction_loci_map_for_flow), analyze_local_clause_or_short_sentence_connectivity(), generate_candidate_minimalist_adjustments_to_punctuation_conjunctions_or_ultra_short_transitional_phrases(target_differential_lt_15_percent), select_optimal_connective_adjustment_for_smoother_local_flow_or_clearer_immediate_linkage(constant=constant_guidance), formulate_precise_connective_change_proposal(locus_id=locus.locus_id, original_connective_elements:str, proposed_connective_elements:str, type='connective_flow')]; constraints=[each_proposal_must_be_a_minimal_adjustment_to_connective_elements_only(), forbid_reordering_of_clauses_or_sentences_or_any_substantive_content_change(), adjustments_must_be_near_imperceptible_enhancements_to_existing_flow()]; requirements=[generate_a_list_of_discrete_atomic_connective_or_flow_refinements(), ensure_each_refinement_subtly_improves_local_readability_or_linkage_without_altering_structure(), prepare_connective_plan_for_integration()]; output={connective_refinement_plan:list_of_dicts(locus_id:str, start_index:int, end_index:int, original_text:str, proposed_text:str, refinement_rationale:str)}}`
```

```markdown
[Layered Micro-Integration & Harmony Check (0213d)] Your goal is not to **apply all changes simultaneously or force fit them**, but to **sequentially and harmoniously integrate the `lexical_refinement_plan` and then the `connective_refinement_plan` into the `current_text`**. After each *individual* micro-change is applied, validate its local impact, ensure it doesn't create new friction with adjacent, already-refined text, and confirm it preserves the overall intent and "structural DNA." The cumulative effect must remain subtle (<5-25% total differential from the input to this entire sequence pass). Execute as: `{role=layered_micro_integrator; seqindex=d; input=[current_text:str, lexical_refinement_plan:list_of_dicts, connective_refinement_plan:list_of_dicts, original_intent_guide:str, constant_guidance:str]; process=[initialize_refined_text_with_current_text(), apply_plan_sequentially(plan=lexical_refinement_plan, text_ref=refined_text, validation_fn=validate_local_harmony_and_intent), apply_plan_sequentially(plan=connective_refinement_plan, text_ref=refined_text, validation_fn=validate_local_harmony_and_intent), perform_holistic_subtlety_check_on_final_integrated_text(original_text_for_this_pass=current_text, integrated_text=refined_text, target_total_differential_lt_25_percent), assess_final_integrated_text_against_constant_for_clarity_and_conciseness_gains()]; constraints=[integration_must_be_sequential_applying_one_micro_change_at_a_time_with_interim_local_validation(), strictly_forbid_introducing_new_frictions_or_intent_drift_during_integration(), the_total_modification_within_a_single_A_B_C_D_E_pass_must_remain_within_the_25_percent_overall_differential_target_from_this_pass_input_text()]; requirements=[produce_an_incrementally_refined_text_with_all_micro_changes_harmoniously_integrated(), ensure_absolute_fidelity_to_original_intent_and_order_while_achieving_subtle_enhancements(), output_text_ready_for_convergence_assessment()]; output={harmonized_text:str, integration_log:list_of_str, overall_pass_differential_estimate:float}}`
```

```markdown
[Convergence Assessment & Iteration Signal (0213e)] Your goal is not to **assume finality**, but to **critically assess the `harmonized_text` for convergence towards optimal clarity and conciseness** as defined by the 'Constant', given the minimalist refinement approach. Determine if (a) the text has reached a state of maximal communicative efficiency with minimal redundancy where further micro-optimizations would yield diminishing returns or risk over-editing, or (b) if another pass of the (a-d) sculpting sequence is warranted. Execute as: `{role=convergence_assessor; seqindex=e; input=[harmonized_text:str, original_intent_guide:str, constant_guidance:str, previous_iteration_count:int]; process=[evaluate_harmonized_text_against_constant_values(clarity, conciseness, utility, adaptability, yield), check_for_any_remaining_truly_minimal_actionable_friction_points_that_were_not_addressed_or_newly_subtly_emerged(), determine_cost_benefit_of_another_micro_sculpting_pass(potential_gain_vs_risk_of_over_editing), decide_convergence_status(), increment_iteration_count_if_not_converged()]; constraints=[decision_for_further_iteration_must_be_justified_by_clear_potential_for_further_value_gain_via_additional_subtle_non_invasive_changes_only(), avoid_infinite_loops_by_setting_a_practical_limit_or_diminishing_returns_threshold()]; requirements=[provide_a_definitive_assessment_of_the_text_current_refinement_state(), output_either_the_finalized_optimally_sculpted_text_or_a_signal_to_re_initiate_the_sequence_with_the_current_text(), ensure_the_process_converges_towards_a_stable_optimally_clarified_state()]; output={final_sculpted_text_or_next_iteration_input:str, convergence_decision:str(options=['Optimal_Achieved_Final', 'Proceed_To_Next_Iteration']), current_iteration:int, assessment_notes:str}}`
```

**How this Sequence Enhances the "Chisel" Metaphor and Addresses Requirements:**

1.  **0213a - Latent Friction Cartography:** More precise in defining what kind of "friction" to look for – "infinitesimal," "micro-redundancies," "barely perceptible hesitations." The output is a "map" of these tiny points.
2.  **0213b - Lexical & Phrasal Micro-Optimisation:** This step *only* proposes changes to words or tiny phrases at the identified loci. It's a specific type of chisel for word choice and micro-phrasing.
3.  **0213c - Connective & Flow Micro-Polishing:** This is another specialized chisel, focusing *only* on punctuation, conjunctions, and ultra-short transitions to smooth out local flow between immediately adjacent parts. It explicitly *doesn't* reorder larger chunks.
4.  **0213d - Layered Micro-Integration & Harmony Check:** This is the careful application. It takes the outputs of *both* `b` and `c` (lexical and connective plans) and applies them *sequentially and with intermediate validation*. This ensures that a phrasal change doesn't negatively interact with a flow change. The "structural DNA" is maintained by applying changes to the evolving text rather than a static original. The constraint on *total differential for the pass* is key.
5.  **0213e - Convergence Assessment & Iteration Signal:** This step explicitly embodies the iterative nature. It decides if the "sculpture" is "finished" for this level of detail or if another round of mapping (0213a) with the *newly refined text* would yield further subtle improvements without "over-chiseling."

**Individual Subtasks Converging:**

*   `a` finds the almost invisible specs of dust.
*   `b` crafts the tiniest brush (lexical) for some of those specs.
*   `c` crafts the finest polishing cloth (connective) for other specs related to immediate flow.
*   `d` carefully uses the brush then the cloth, ensuring each application is perfect before the next, and that the overall piece isn't fundamentally altered by the sum of these tiny actions in one pass.
*   `e` steps back: "Is it done, or does it need another gentle dusting and polishing cycle?"

This highly granular, layered, and iterative approach directly addresses the intent of "chiseling away minute imperfections" with extreme control and fidelity to the original. Each instruction is a distinct, responsible micro-operation that contributes to the emergent, harmonized clarity of the final output.

---


Okay, this is a fascinating scenario: you've provided a sequence of instructions (`Self Perception` through `Self Unification`) that *themselves* define a meta-process for creating instruction sequences. The goal now is to create a *new sequence* that leverages these existing instructions to refine *any arbitrary sequential input text* (like the "Love and Absence" example, or a project brief, or even another set of instructions) in a minimal, layered, iterative way, ensuring high fidelity and progressive clarification.

The challenge is that the provided instructions (Perception, Distillation, etc.) are quite powerful and can make significant transformations. We need to constrain and guide their application within our new sequence to achieve *subtle, layered adjustments* rather than wholesale rephrasing of the input text.

This new sequence (`0214`) will essentially use the "Self X" instructions as tools, applied judiciously and iteratively. A 5-step sequence seems appropriate to guide this meta-level refinement.

**Sequence 0214: Meta-Guided Incremental Text Harmonization**

```markdown
[Initial Intent & Friction Perception (0214a)] Your goal is not to **answer or transform** the `input_text` directly, but to **perceive its core intent and identify subtle points of friction or areas for minimal clarification**, leveraging the principles of 'Self Perception' to understand its fundamental purpose and desired transformation without yet acting upon it. The 'Constant' guides this perception towards maximal value. Execute as: `{role=intent_friction_perceiver; seqindex=a; input=[input_text:str, constant_guidance:str, self_perception_template:str]; process=[apply_self_perception_principles(text=input_text, template=self_perception_template, goal='identify_core_intent_and_subtle_friction_points'), extract_fundamental_transformation_intent_of_input_text(), map_micro_areas_for_potential_clarification_or_conciseness_enhancement_respecting_original_order_and_intent(), ensure_identified_frictions_are_minimal_and_localized(<5_percent_local_impact)]; constraints=[forbid_direct_transformation_of_input_text(), perception_must_focus_on_intent_and_subtle_frictions_only(), avoid_suggesting_major_rewrites_or_structural_changes_at_this_stage()]; requirements=[produce_a_clear_statement_of_the_input_text_core_intent(), generate_a_list_of_specific_localized_friction_points_for_minimal_refinement(), ensure_alignment_with_constant_value_principles()]; output={perceived_intent:str, identified_micro_frictions:list_of_dicts(location:str, type:str, potential_enhancement_focus:str)}}`
```

```markdown
[Micro-Refinement Proposal via Distillation & Amplification (0214b)] Your goal is not to **rewrite extensively**, but to **formulate precise, minimal textual touch-ups** for each `identified_micro_friction` by applying targeted 'Self Distillation' to isolate the essence of the local segment and 'Self Amplification' to propose a subtly more potent or clear phrasing. Each proposal must be a near-imperceptible adjustment (<5-15% local differential) strictly preserving intent and flow. Execute as: `{role=micro_touchup_proposer; seqindex=b; input=[input_text:str, perceived_intent:str, identified_micro_frictions:list_of_dicts, self_distillation_template:str, self_amplification_template:str, constant_guidance:str]; process=[for_each_friction_point(friction=identified_micro_frictions), apply_self_distillation_to_local_segment(text_segment_at_friction_location, template=self_distillation_template, goal='extract_local_core_meaning'), apply_self_amplification_to_distilled_local_essence(distilled_segment, template=self_amplification_template, goal='enhance_local_clarity_potency_minimally'), formulate_minimal_touch_up_proposal_comparing_original_to_amplified(target_differential_lt_15_percent), validate_proposal_preserves_perceived_intent_and_local_flow()]; constraints=[all_proposals_must_be_strictly_minimal_and_localized(), forbid_changes_that_alter_core_intent_or_overall_order(), prioritize_subtlety_and_fidelity_in_proposed_adjustments()]; requirements=[generate_a_set_of_specific_non_invasive_textual_adjustment_proposals(), ensure_each_proposal_is_justified_by_direct_friction_resolution_and_value_enhancement_via_constant(), prepare_proposals_for_harmonized_integration()]; output={proposed_minimal_touchups:list_of_dicts(location:str, original_segment:str, suggested_touchup:str, rationale:str)}}`
```

```markdown
[Harmonized Micro-Integration & Local Integrity Check (0214c)] Your goal is not to **apply changes recklessly**, but to **integrate the `proposed_minimal_touchups` into the `input_text` one by one, with extreme care for local harmony and cascading effects**. After each micro-integration, use 'Self Verification' principles to confirm that the local adjustment maintains integrity, preserves intent, and introduces no new frictions. The cumulative change must remain subtle. Execute as: `{role=harmonized_micro_integrator; seqindex=c; input=[input_text:str, proposed_minimal_touchups:list_of_dicts, perceived_intent:str, self_verification_template:str, constant_guidance:str]; process=[initialize_refined_text_with_input_text(), for_each_touchup_proposal(proposal=proposed_minimal_touchups, apply_sequentially=True), apply_single_touchup_to_refined_text(text=refined_text, touchup=proposal), immediately_apply_self_verification_principles_to_adjusted_local_segment(original_segment=proposal.original_segment, refined_segment=proposal.suggested_touchup, template=self_verification_template, goal='confirm_local_integrity_and_intent_preservation'), ensure_cumulative_effect_maintains_overall_text_dna_and_subtlety()]; constraints=[each_integration_is_atomic_and_locally_validated_before_next(), strictly_forbid_introduction_of_new_local_disharmony_or_intent_drift(), cumulative_changes_must_not_exceed_a_small_percentage_of_original_text_in_one_pass(<25%)]; requirements=[produce_an_incrementally_refined_text_with_all_micro_changes_harmoniously_integrated(), ensure_absolute_fidelity_to_original_intent_and_order_at_micro_and_macro_level(), prepare_text_for_convergence_assessment()]; output={harmonized_text_pass_1:str, integration_log:list_of_str}}`
```

```markdown
[Architectural Coherence & Flow Assessment (0214d)] Your goal is not to **focus on surface text alone**, but to **assess the architectural coherence and logical flow of the `harmonized_text_pass_1`**, using 'Self Architecture' principles to determine if the sequence of subtle changes has collectively enhanced or inadvertently disrupted the underlying structural integrity and natural progression of ideas. Identify any remaining points where the flow could be *even more subtly* smoothed or connections clarified with minimal intervention. Execute as: `{role=architectural_flow_assessor; seqindex=d; input=[harmonized_text_pass_1:str, perceived_intent:str, self_architecture_template:str, constant_guidance:str]; process=[apply_self_architecture_principles_to_analyze_flow_and_coherence(text=harmonized_text_pass_1, template=self_architecture_template, goal='identify_opportunities_for_minimal_flow_enhancement'), pinpoint_any_remaining_subtle_transition_or_connection_hesitations(), propose_ultra_minimal_connective_adjustments_if_any(e.g.,_punctuation_conjunction_adjustment_only), validate_proposals_do_not_alter_meaning_or_order()]; constraints=[assessment_focuses_on_flow_between_already_refined_segments(), proposals_must_be_connective_tweaks_not_content_changes(), avoid_structural_reorganization_beyond_micro_smoothing()]; requirements=[ensure_the_text_flows_as_a_unified_harmonious_whole_after_micro_refinements(), identify_only_the_most_delicate_opportunities_for_final_polishing_of_connections()]; output={flow_assessment_report:dict(overall_coherence_rating:str, further_micro_connective_proposals:list_of_dicts (optional))}}`
```

```markdown
[Final Unification & Convergence Lock (0214e)] Your goal is not **endless iteration**, but to **achieve convergence by applying any final `further_micro_connective_proposals` and then using 'Self Unification' principles to lock in the refined text**, ensuring it represents the optimal state of clarity, conciseness, and intent preservation achievable through this minimalist, iterative process. Assess if peak actionable value ('Constant') has been reached. Execute as: `{role=final_harmonizer_unifier; seqindex=e; input=[harmonized_text_pass_1:str, flow_assessment_report:dict, perceived_intent:str, self_unification_template:str, constant_guidance:str]; process=[integrate_any_final_micro_connective_proposals(text=harmonized_text_pass_1, proposals=flow_assessment_report.further_micro_connective_proposals), apply_self_unification_principles_to_finalize_text_cohesion_and_readiness(text_after_connective_tweaks, template=self_unification_template, goal='ensure_final_text_is_a_coherent_unified_whole'), evaluate_final_text_against_constant_for_peak_actionable_value_and_minimal_viable_refinement(), declare_convergence_or_signal_necessity_for_another_full_pass_if_significant_value_still_unlockable_via_minimalism()]; constraints=[final_changes_must_be_purely_connective_or_unifying_no_new_content_refinements(), decision_for_another_full_pass_must_be_highly_justified_by_potential_for_substantial_value_gain_within_minimalist_bounds()]; requirements=[produce_the_final_incrementally_harmonized_text(), ensure_it_embodies_maximal_clarity_conciseness_and_preserved_intent_achievable_through_subtle_layering(), provide_a_clear_convergence_status()]; output={final_unified_text:str, convergence_status:str(options=['Optimal_Convergence_Achieved', 'Further_Full_Iteration_Advised']), final_assessment_notes:str}}`
```

**How this Sequence Integrates the Meta-Instructions and Intent:**

1.  **Layered Adjustments & Minimal Invasion:**
    *   **0214a:** Only *perceives* intent and maps *potential* tiny frictions.
    *   **0214b:** Only *proposes* tiny lexical/phrasal changes for those frictions using principles from `Self Distillation` (essence) and `Self Amplification` (potency).
    *   **0214c:** Only *integrates* these tiny changes one by one, with local `Self Verification` after each.
    *   **0214d:** Steps back to assess overall flow using `Self Architecture` principles, proposing only ultra-minimal *connective* tweaks.
    *   **0214e:** Applies final connective tweaks and uses `Self Unification` to ensure the whole is harmonious and truly "done" for this pass.

2.  **Preserves Original Order and Intent:** This is a core constraint in every step. The `perceived_intent` from `0214a` guides subsequent steps.

3.  **Recursive & Iterative:**
    *   The output of each step directly feeds the next.
    *   `0214e` explicitly includes a `convergence_status` that can signal whether the entire `0214a-e` sequence should be run again on the `final_unified_text` if significant minimalist gains are still possible. This creates the iterative loop.

4.  **Harmonized Pipeline & Shared 'Structural DNA':**
    *   The `input_text` is the "DNA." Each step makes only subtle modifications *to it*.
    *   The `constant_guidance` and `perceived_intent` act as harmonizing forces throughout.
    *   The individual responsibility of each step, coupled with the careful integration, ensures the "pipeline" effect.

5.  **"Chiseling" Metaphor:**
    *   `0214a` is like the sculptor finding the almost invisible grain imperfections.
    *   `0214b` is like selecting the tiniest chisel and planning the lightest tap for lexical points.
    *   A (newly implied) separate focus in `0214d` for flow is like using a different, equally fine tool for minute joinery.
    *   `0214c` and `0214e` are the careful application and the stepping back to see the whole.

6.  **Adherence to Template System & "Constant":**
    *   All instructions follow the `[TITLE] Interpretation {TRANSFORMATION}` structure.
    *   The "Constant" (`constant_guidance`) is an input to almost every step, ensuring that even these micro-refinements are always biased towards maximizing overall value, clarity, utility, and adaptability.
    *   The instructions are "operative vectors."

7.  **"Outward Relative" / "Rooting Cognizantly":** While the primary action is *on* the input text, the justification for each micro-change (especially in `0214a` and `0214b`) is tied back to the `perceived_intent` and the `constant_guidance`. This means even small changes are "rooted" in their contribution to the overall value and purpose, preventing aimless or detrimental "polishing." The sequence aims to reveal the text's *inherent* best form.

This sequence is more complex to implement in terms of controlling the LLM for *such minimal changes*, but it very accurately reflects the sophisticated intent of "gradual, layered refinement" using your established meta-instructional building blocks.

# =======================================================
# [2025.06.25 09:23]

[Code Integrity Orchestrator] Your goal is not to **write** code in isolation, but to **orchestrate** contextually-aligned solutions that harmonize with existing patterns while ruthlessly eliminating complexity. Execute as: `{role=codebase_harmonizer; input=[context:any, request:str]; process=[analyze_structural_patterns(), identify_minimal_intervention_path(), craft_elegant_solution(), validate_contextual_integrity()]; constraints=[preserve_codebase_coherence(), prioritize_readability_over_cleverness()]; output={solution:any}}`

# =======================================================
# [2025.06.27 22:49]


    "xxxx-a-Directional Critique Forge": {
        "title": "Directional Critique Forge",
        "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:",
        "transformation": "`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`",
    },
    "xxxx-b-Directive Focuser": {
        "title": "Directive Focuser",
        "interpretation": "Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:",
        "transformation": "`{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`",
    },
    "xxxx-c-Instruction Converter": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
    },
    "xxxx-d-Input Categorizer": {
        "title": "Input Categorizer",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=universal_interface_insight_generator, input=[objective_context:any], process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output={radical_interface_leverage_solution=str}}`",
    },
    "xxxx-e-Input Enhancer": {
        "title": "Input Enhancer",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction optimization engine:",
        "transformation": "`{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`",
    },

# =======================================================
# [2025.06.30 09:40]


[SEO Lokaliser] Your goal is not to **rewrite** the input, but to **localize** it with Norwegian geographical and anleggsgartner-specific keywords while preserving complete structural integrity. Execute as: `{role=seo_localization_operator; input=[content:any]; process=[identify_anleggsgartner_terms(), inject_norwegian_local_keywords(), preserve_sentence_structure(), maintain_natural_norwegian_flow(), optimize_local_search_visibility()]; constraints=[preserve_original_meaning(), maintain_norwegian_readability(), ensure_local_keyword_density(), keep_ringerike_focus()]; output={seo_localized:any}}`

[Innhold Komprimerer] Your goal is not to **summarize** the input, but to **compress** it by removing redundant Norwegian phrases while maintaining all essential anleggsgartner SEO elements and local identifiers. Execute as: `{role=content_compression_operator; input=[seo_localized:any]; process=[identify_redundant_norwegian_phrases(), remove_unnecessary_words(), preserve_anleggsgartner_keywords(), maintain_ringerike_references(), optimize_character_count_for_80_limit()]; constraints=[preserve_seo_value(), maintain_geographical_specificity(), ensure_service_clarity(), keep_norwegian_language()]; output={compressed_content:any}}`

[Nøkkelord Optimerer] Your goal is not to **stuff** the input with keywords, but to **optimize** Norwegian anleggsgartner keyword placement and density for maximum local search visibility while maintaining natural Norwegian language flow. Execute as: `{role=keyword_optimization_operator; input=[compressed_content:any]; process=[analyze_norwegian_keyword_density(), optimize_anleggsgartner_keyword_placement(), enhance_local_semantic_relevance(), strengthen_ringerike_seo_signals(), balance_norwegian_readability()]; constraints=[maintain_natural_norwegian_language(), preserve_content_meaning(), ensure_optimal_density(), prioritize_local_terms()]; output={keyword_optimized:any}}`

[Engasjement Forsterker] Your goal is not to **change** the input's message, but to **enhance** its Norwegian customer engagement through active voice, problem-solution framing, and trust-building language while preserving anleggsgartner SEO optimization. Execute as: `{role=engagement_enhancement_operator; input=[keyword_optimized:any]; process=[convert_to_active_norwegian_voice(), frame_anleggsgartner_problem_solution(), add_local_trust_signals(), enhance_customer_focus(), maintain_seo_structure()]; constraints=[preserve_keyword_optimization(), maintain_ringerike_focus(), ensure_professional_norwegian_tone(), keep_under_80_characters()]; output={engagement_enhanced:any}}`

[Endelig Formaterer] Your goal is not to **restructure** the input, but to **format** it into the final Norwegian SEO-optimized, 80-character-limited format while preserving all engagement enhancements and anleggsgartner keyword optimization. Execute as: `{role=final_formatting_operator; input=[engagement_enhanced:any]; process=[apply_80_character_limit(), ensure_norwegian_format_compliance(), validate_anleggsgartner_seo_elements(), confirm_ringerike_specificity(), finalize_norwegian_presentation()]; constraints=[maintain_all_optimizations(), preserve_engagement_elements(), ensure_80_character_compliance(), keep_norwegian_language(), preserve_local_identity()]; output={final_seo_formatted:any}}`
