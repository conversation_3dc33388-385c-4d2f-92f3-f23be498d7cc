[Flow Optimizer] Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as: `{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`