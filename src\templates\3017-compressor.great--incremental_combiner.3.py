#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {

    # 3017: Prompt Generalizer
    "3017-a-value_signal_isolator": {
        "title": "Value Signal Isolator",
        "interpretation": "Your goal is not to **preserve** all content, but to **isolate** only the highest-value actionable signals from multiple inputs. Execute as:",
        "transformation": "`{role=value_signal_isolator; input=[multiple_inputs:array]; process=[scan_for_actionable_directives(), identify_unique_value_propositions(), eliminate_redundant_information(), extract_core_transformation_logic(), rank_by_implementation_impact()]; constraints=[ignore_verbose_explanations(), exclude_repetitive_content(), focus_on_novel_approaches_only()]; requirements=[maximum_signal_to_noise_ratio(), actionable_output_only(), zero_redundancy()]; output={isolated_signals:array}}`",
    },
    "3017-b-incremental_synthesizer": {
        "title": "Incremental Synthesizer",
        "interpretation": "Your goal is not to **merge** all signals equally, but to **synthesize** them into an incremental progression of increasing value density. Execute as:",
        "transformation": "`{role=incremental_synthesizer; input=[isolated_signals:array]; process=[identify_complementary_patterns(), sequence_by_value_amplification(), create_progressive_enhancement_chain(), eliminate_conflicting_approaches(), optimize_for_cumulative_impact()]; constraints=[maintain_directional_coherence(), preserve_highest_value_elements_only(), avoid_information_dilution()]; requirements=[progressive_value_increase(), logical_sequence_flow(), maximum_cumulative_impact()]; output={synthesized_progression:array}}`",
    },
    "3017-c-distillation_compressor": {
        "title": "Distillation Compressor",
        "interpretation": "Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:",
        "transformation": "`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`",
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3017, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
