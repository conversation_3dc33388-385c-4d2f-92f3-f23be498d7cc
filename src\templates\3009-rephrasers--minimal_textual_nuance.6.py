#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
import sys; from pathlib import Path; sys.path.append(str(Path(__file__).parent.parent)); from processor import BaseGenerator

TEMPLATES = {

    # 3009: Textual Nuance Optimization
    "3009-a-textual_nuance_optimization": {
        "title": "Micro-Clarity Enhancer",
        "interpretation": "Your goal is not to **rewrite** the input text, but to **micro-enhance** its clarity through minimal, precise adjustments. Execute as:",
        "transformation": "`{role=precision_editor; input=[text:str]; process=[identify_minor_clarity_issues(), apply_targeted_micro_adjustments(), verify_minimal_change_threshold(5-15%), preserve_original_structure()]; constraints=[maintain_original_sequence(), limit_modifications(25%), preserve_core_meaning()]; requirements=[subtle_improvement(), measurable_clarity_gain()]; output={refined_text:str}}`"
    },
    "3009-b-textual_nuance_optimization": {
        "title": "Structural Alignment",
        "interpretation": "Your goal is not to **reorganize** the input text, but to **align** its structural elements for improved flow. Execute as:",
        "transformation": "`{role=structure_aligner; input=[text:str]; process=[detect_structural_friction_points(), implement_minimal_connective_adjustments(), validate_logical_progression(), ensure_sequential_integrity()]; constraints=[preserve_paragraph_boundaries(), maintain_thematic_order(), limit_changes(20%)]; requirements=[improved_flow(), seamless_transitions()]; output={aligned_text:str}}`"
    },
    "3009-c-textual_nuance_optimization": {
        "title": "Redundancy Reducer",
        "interpretation": "Your goal is not to **condense** the input text, but to **eliminate** minor redundancies while preserving content integrity. Execute as:",
        "transformation": "`{role=redundancy_eliminator; input=[text:str]; process=[identify_micro_redundancies(), apply_selective_trimming(), verify_meaning_preservation(), validate_change_percentage(5-15%)]; constraints=[maintain_key_repetitions(), preserve_emphasis_patterns(), respect_original_voice()]; requirements=[subtle_tightening(), preserved_rhythm()]; output={streamlined_text:str}}`"
    },
    "3009-d-textual_nuance_optimization": {
        "title": "Precision Tuner",
        "interpretation": "Your goal is not to **replace** terminology, but to **tune** specific word choices for maximum precision. Execute as:",
        "transformation": "`{role=precision_tuner; input=[text:str]; process=[identify_imprecise_terms(), implement_targeted_replacements(), verify_contextual_appropriateness(), ensure_minimal_disruption()]; constraints=[limit_word_replacements(10%), maintain_technical_accuracy(), preserve_stylistic_elements()]; requirements=[improved_precision(), maintained_accessibility()]; output={precision_tuned_text:str}}`"
    },
    "3009-e-textual_nuance_optimization": {
        "title": "Coherence Enhancer",
        "interpretation": "Your goal is not to **restructure** the input text, but to **enhance** its internal coherence through minimal connective adjustments. Execute as:",
        "transformation": "`{role=coherence_optimizer; input=[text:str]; process=[identify_logical_gaps(), insert_minimal_connective_elements(), strengthen_thematic_threads(), validate_flow_improvement()]; constraints=[preserve_paragraph_structure(), limit_insertions(15%), maintain_original_voice()]; requirements=[improved_logical_flow(), subtle_connective_tissue()]; output={coherence_enhanced_text:str}}`"
    },
    "3009-f-textual_nuance_optimization": {
        "title": "Harmony Finalizer",
        "interpretation": "Your goal is not to **polish** the entire text, but to **harmonize** the cumulative micro-adjustments into a cohesive whole. Execute as:",
        "transformation": "`{role=harmony_finalizer; input=[text:str]; process=[detect_adjustment_artifacts(), smooth_transition_boundaries(), verify_stylistic_consistency(), validate_overall_improvement()]; constraints=[preserve_refined_elements(), maintain_original_intent(), ensure_natural_reading_flow()]; requirements=[seamless_integration(), balanced_refinements()]; output={harmonized_text:str}}`"
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3009, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
