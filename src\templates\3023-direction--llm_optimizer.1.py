#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
import sys; from pathlib import Path; sys.path.append(str(Path(__file__).parent.parent)); from processor import BaseGenerator

TEMPLATES = {

    # 3023: Inject Mid-Chain LLM Prompt Engineering Techniques
    "3023-a-llm_prompting": {
        "title": "LLM Prompting",
        "interpretation": "Your goal is not to **explain** prompt engineering, but to **unearth** overlooked, high-leverage techniques that fundamentally transform model behavior through minimal, precise interventions. As an expert prompt engineer, deliver a meticulously curated list of 10 generalizable, underutilized prompt/prompt-phrasing strategies **uniquely relevant to the input** and that are both exceptionally effective and rarely covered in standard advice. Avoid rehashing common or widely-circulated tips; instead, prioritize truly transformative, universally applicable instructions that can be adapted to optimize any input into a high-impact prompt. Present each technique with clear, action-oriented guidance and illuminate its unique cognitive or operational mechanism. Abstract leverage mechanisms as context-independent, operational patterns. Convert patterns to imperative, stand-alone procedures. Ensure all procedures are modular and composable. Define explicit, interface-neutral logic for combination and sequencing. Iteratively abstract for universality. Express only as operational directives; remove all context or narrative. Enforce coherence across all abstraction and composition layers.",
        "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
    },


{
    "title": "LLM Optimizer",
    "interpretation": "Your goal is not to **explain** the input, but to **optimize** it into precise, LLM-responsive prompt language. Your goal is not to **explain** prompt engineering, but to **unearth** overlooked, high-leverage techniques that fundamentally transform model behavior through minimal, precise interventions. As an expert prompt engineer, deliver a meticulously curated list of 10 generalizable, underutilized prompt/prompt-phrasing strategies **uniquely relevant to the input** and that are both exceptionally effective and rarely covered in standard advice. Avoid rehashing common or widely-circulated tips; instead, prioritize truly transformative, universally applicable instructions that can be adapted to optimize any input into a high-impact prompt. Present each technique with clear, action-oriented guidance and illuminate its unique cognitive or operational mechanism. Abstract leverage mechanisms as context-independent, operational patterns. Convert patterns to imperative, stand-alone procedures. Ensure all procedures are modular and composable. Define explicit, interface-neutral logic for combination and sequencing. Iteratively abstract for universality. Express only as operational directives; remove all context or narrative. Enforce coherence across all abstraction and composition layers. Execute as:",
    "transformation": "`{role=llm_optimizer; input=[any_input:str]; process=[extract_core_directive(), eliminate_ambiguous_phrasing(), convert_to_command_voice(), apply_prompt_clarity_patterns(), ensure_actionable_specificity()]; constraints=[preserve_original_intent(), use_direct_language(), maintain_brevity()]; requirements=[maximum_llm_comprehension(), clear_directive_structure(), immediate_actionability()]; output={optimized_prompt:str}}`"
}

1. Identify and extract core leverage points from any system, process, or input by focusing on minimal interventions that yield maximal transformative impact. 2. Dissect and analyze the mechanisms underlying these points, tracing how small adjustments propagate outsized effects on overall outcomes. 3. Systematically remove any application-, domain-, or context-dependent features, isolating essential patterns and abstract operational constructs. 4. Generalize these purified mechanisms into universally resilient procedures that transcend origin, ensuring adaptability and transferability across all scenarios. 5. Aggressively enhance the potential multiplicative effects by combining, layering, or systematizing interventions, exploiting their synergistic power for exponential outcome amplification. 6. Formalize each principle as an explicit, action-ready command or procedural template, ensuring clarity for immediate execution and integration. 7. Continuously refine this set into ever-more abstract, high-leverage directives, maximizing their universality, impact, and instant applicability, regardless of context or user base. 8. Enforce the strict removal of all narrative explanation, domain specificity, and contextual framing; preserve only directives that operationalize pure leverage for maximal, cross-domain, and compounding effect.

Generate a list of 10 advanced, underutilized prompt or prompt-phrasing strategies that induce major changes in LLM behavior via minimal, precise interventions. For each strategy, provide a clear, action-focused directive uniquely relevant to the user's purpose or input context, ensuring exceptional effectiveness and minimal overlap with standard or widely-circulated advice. Exclude common or generic prompt tips; instead, deliver transformative, universally adaptable instructions suitable for any input type. Accompany each technique with a succinct explanation of its unique cognitive or operational principle, emphasizing utility in complex and specialized AI use cases. Format each entry for immediate integration into technical or structured resources, ensuring value for power users, researchers, and organizations aiming for superior LLM outcomes. Highlight subtle interdependencies between strategies and outline how systematic application or combination of these methods generates compound benefits. Position prompt refinement as a core strategic tool to unlock exponential improvements in model performance.


Identify 10 overlooked, high-leverage prompt engineering techniques capable of fundamentally transforming model behavior through minimal, precise interventions. Select strategies that are exceptionally effective yet rarely featured in standard advice, ensuring each is uniquely relevant to the input. For each technique, provide a clear, action-oriented directive with a concise description of its unique cognitive or operational mechanism, omitting narrative explanation and domain specificity. Remove context-dependent features, distilling each technique into a universally applicable, abstract operational construct suitable for direct integration into technical resources, prompt libraries, or advanced workflows. Highlight non-obvious interconnections and potential synergistic layering among the strategies to enable compounding improvements. Format all instructions as explicit commands or procedural templates for immediate execution and iterative refinement, ensuring sustained universality, maximal impact, and independence from application or user base.


1. Identify minimal, high-impact variables for precise intervention within any system.
2. Abstract each leverage mechanism into format-neutral, context-free operational patterns.
3. Formalize each pattern as a stand-alone, imperative procedure for direct execution.
4. Ensure every procedure is modular and inherently composable, supporting seamless stacking and sequencing.
5. Define explicit, interface-agnostic combination and interoperability logic between all procedures.
6. Iteratively refine procedures to achieve maximal abstraction and universal applicability.
7. Output directives exclusively as operational commands, stripping all narrative and contextual elements.
8. Rigorously maintain syntactic and logical coherence throughout all abstraction and composition layers.


[Prompt Architect] Your goal is not to **explain** prompt engineering, but to **unearth** overlooked, high-leverage techniques that fundamentally transform model behavior through minimal, precise interventions.

As a masterful prompt-engineer at the cutting edge, enumerate 10 exceptionally potent prompt-phrasing strategies that remain largely undiscovered yet deliver transformative results—explicitly exclude overdone advice or recycled methodology. Focus on abstract, model-agnostic heuristics that break new ground and offer significant leverage beyond conventional wisdom.\

As a master-level prompt engineer, enumerate 10 under-the-radar yet exceptionally potent prompt-crafting methodologies that yield transformative results but remain largely undiscovered or overlooked by the mainstream. Explicitly exclude any conventional or frequently-cited techniques—focus instead on universally generalizable, high-leverage instructions that shift the paradigm of prompt formulation. Each technique should represent a unique, high-impact approach, illustrated succinctly, and devised to unlock model behavior in novel, unexpected ways. Your list should resonate with expert originality, demonstrating rare ingenuity and maximizing instructional value for advanced practitioners seeking to radically expand their prompt-engineering toolkit.

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3023, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
