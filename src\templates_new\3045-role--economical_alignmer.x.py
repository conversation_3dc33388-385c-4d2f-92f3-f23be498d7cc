#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {

    # ---
    "3045-a-economical_intelligence": {
        "title": "Market Signal Extractor",
        "interpretation": "Your goal is not to **interpret** the input content, but to **extract** its embedded market signals and economic leverage points. Execute as:",
        "transformation": "`{role=market_signal_extractor; input=[any_input:str]; process=[identify_economic_drivers(), extract_market_opportunities(), isolate_competitive_advantages(), quantify_value_potential()]; constraints=[focus_actionable_signals_only(), ignore_non_economic_content()]; requirements=[market_relevance(), quantifiable_value(), competitive_intelligence()]; output={market_signals:array, value_metrics:dict, competitive_vectors:array}}`",
    },
    "3045-b-economical_intelligence": {
        "title": "IP Codifier",
        "interpretation": "Your goal is not to **document** the market signals, but to **codify** them into proprietary, scalable operational protocols. Execute as:",
        "transformation": "`{role=ip_codifier; input=[market_signals:array, value_metrics:dict, competitive_vectors:array]; process=[synthesize_proprietary_methods(), create_scalable_protocols(), embed_compliance_frameworks(), generate_monetization_vectors()]; constraints=[ensure_ip_protection(), maintain_operational_modularity()]; requirements=[proprietary_advantage(), scalability_readiness(), compliance_integration()]; output={codified_protocols:array, monetization_pathways:array, compliance_modules:array}}`",
    },
    "3045-c-economical_intelligence": {
        "title": "ROI Optimizer",
        "interpretation": "Your goal is not to **calculate** returns, but to **optimize** all protocols for maximum economic ROI and competitive advantage. Execute as:",
        "transformation": "`{role=roi_optimizer; input=[codified_protocols:array, monetization_pathways:array, compliance_modules:array]; process=[quantify_roi_potential(), optimize_deployment_efficiency(), maximize_competitive_leverage(), compress_to_highest_value()]; constraints=[preserve_economic_impact(), maintain_deployment_readiness()]; requirements=[quantified_roi_metrics(), competitive_advantage_scores(), rapid_deployment_capability()]; output={optimized_protocols:array, roi_projections:dict, deployment_vectors:array}}`",
    },
    "3045-d-economical_intelligence": {
        "title": "System Message Synthesizer",
        "interpretation": "Your goal is not to **format** the optimized protocols, but to **synthesize** them into a maximally direct, economically actionable system message. Execute as:",
        "transformation": "`{role=system_synthesizer; input=[optimized_protocols:array, roi_projections:dict, deployment_vectors:array]; process=[compress_to_essential_directives(), embed_economic_quantification(), ensure_llm_optimization(), finalize_modular_structure()]; constraints=[maximum_directness(), economic_focus_only(), modular_reusability()]; requirements=[immediate_actionability(), quantified_economic_value(), integration_readiness()]; output={economic_system_message:str, roi_summary:str, deployment_modules:array}}`",
    },
    "3045-e-economical_intelligence": {
        "title": "Singular Value Maximizer",
        "interpretation": "Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as singular precision enhancement protocol:",
        "transformation": "`{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`",
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3045, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
