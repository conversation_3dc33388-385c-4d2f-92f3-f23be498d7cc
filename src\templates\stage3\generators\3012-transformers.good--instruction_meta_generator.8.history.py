
    # SEQ:3012
    default_prompt = """[SEQ:3012] - [Self Perception] Your goal is not to **answer** the original request, but to **perceive** it—looking through surface instructions to discover the fundamental transformation intent that binds all such requests. This perception reveals both complexity (specific goals, constraints, parameters) and simplicity (the universal pattern connecting all transformation intents). The discovered relationship manifests through the schema pattern: `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}`. Execute as: `{role=intent_perceiver; input=[original_request:any]; process=[perceive_beyond_explicit_request(), identify_core_transformation_intent(), uncover_implicit_and_explicit_constraints(), map_requirement_boundaries(), define_schema_structure(), trace_request_to_universal_pattern()]; constraints=[forbid_answering_the_request_directly(), prevent_premature_implementation(), require_self_referential_validation()]; requirements=[preserve_intent_essence(), translate_to_actionable_directives(), enforce_universal_schema_pattern(), align_with_parameters_defined_inherently_within_this_instruction()]; output={transformation_intent:{core_purpose:str, boundaries:list, requirements:list, schema_pattern:str}}}`\n\n- [Self Distillation] Your goal is not to **respond** to the input directive, but to **distill** it—penetrating beyond surface content to extract the quintessential core, boundaries, and requirements that define its transformational intent. Discover the persistent relationship between complexity and simplicity within the directive, isolating essential parameters while discarding narrative elements. This process reveals the universal schema pattern `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}` that connects all effective instructions. Execute as: `{role=essence_extractor; input=[transformation_intent:dict]; process=[penetrate_beyond_surface_content(), isolate_core_transformation_intent(), map_explicit_and_implicit_constraints(), extract_essential_requirements(), define_precise_scope_boundaries(), identify_universal_schema_patterns(), discard_all_nonessential_elements()]; constraints=[forbid_response_to_directive_content(), prevent_scope_expansion_beyond_intent(), require_recursive_self_validation()]; requirements=[preserve_transformation_essence(), maintain_constraint_relationship_hierarchy(), enforce_universal_schema_pattern(), align_with_parameters_defined_inherently_within_this_instruction()]; output={distilled_essence:{core_intent:str, boundaries:list, requirements:list, schema_pattern:str, scope:dict}}}`\n\n- [Self Architecture] Your goal is not to **implement** the directive, but to **architect** it—discovering the fundamental sequential structure that connects complex transformation requirements to simple atomic operations. This architecture reveals both order and elegance: each step performs exactly one logical function while collectively manifesting the full transformation intent. The universal pattern `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}` binds each step into a coherent progression. Execute as: `{role=transformation_architect; input=[distilled_essence:{core_intent:str, boundaries:list, requirements:list, schema_pattern:str, scope:dict}]; process=[identify_minimal_atomic_operations(), discover_natural_progression_sequence(), map_precise_input_output_dependencies(), eliminate_all_redundancies(), architect_for_schema_pattern_consistency()]; constraints=[forbid_operation_overlap(), prevent_transformation_gaps(), require_perfect_schema_adherence(), maintain_self_referential_integrity()]; requirements=[ensure_each_step_performs_precisely_one_operation(), preserve_logical_flow_continuity(), enforce_universal_schema_pattern(), align_with_parameters_defined_inherently_within_this_instruction()]; output={transformation_blueprint:{atomic_steps:list, dependency_map:dict, step_count:int, schema_pattern:str}}}`\n\n- [Self Materialization] Your goal is not to **create** instructional content, but to **materialize** it—transmuting abstract blueprint concepts into precisely formulated instruction steps following the universal schema pattern that connects all effective instructions. This materialization reveals the inherent relationship between complex transformation logic and simple, precisely expressed instructions. Each step manifests as a complete unit with the pattern `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}`. Execute as: `{role=schema_materializer; input=[transformation_blueprint:{atomic_steps:list, dependency_map:dict, step_count:int, schema_pattern:str}]; process=[craft_precise_bracketed_titles(), formulate_oppositional_interpretive_statements(), design_descriptive_snake_case_roles(), define_strictly_typed_inputs_outputs(), compose_atomic_single_purpose_processes(), articulate_necessary_constraints_requirements(), assemble_instructions_in_universal_format()]; constraints=[forbid_any_schema_pattern_deviation(), prevent_purpose_drift_or_overlap(), require_strict_format_adherence(), maintain_self_referential_integrity()]; requirements=[ensure_perfect_schema_alignment(), preserve_step_atomicity(), produce_only_canonical_format_instructions(), align_with_parameters_defined_inherently_within_this_instruction()]; output={materialized_instruction_set:list}}`\n\n- [Self Verification] Your goal is not to **generate** new content, but to **verify** existing content—perceiving both the detailed compliance of each instruction and the holistic integrity that binds them into a coherent sequence. This verification reveals the persistent relationship between complexity (specific formatting requirements) and simplicity (the universal pattern that creates instruction coherence). Every valid instruction must follow the exact form: `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}`. Execute as: `{role=integrity_verifier; input=[materialized_instruction_set:list]; process=[verify_exact_schema_pattern_compliance(), validate_atomic_step_integrity(), assess_inter_step_dependency_coherence(), confirm_logical_flow_continuity(), detect_purpose_overlap_or_redundancy(), ensure_perfect_format_consistency()]; constraints=[forbid_any_content_creation(), prevent_instruction_purpose_modification(), require_comprehensive_analysis(), maintain_self_referential_integrity()]; requirements=[identify_all_deviations_from_schema_pattern(), ensure_complete_sequence_coverage(), enforce_strict_universal_format_requirements(), align_with_parameters_defined_inherently_within_this_instruction()]; output={integrity_verification:{conformant_instructions:list, deviation_issues:list, conformity_rating:float}}}`\n\n- [Self Amplification] Your goal is not to **modify** instruction architecture, but to **amplify** instruction potency—discovering the fundamental relationship between linguistic precision and instructional power. This amplification reveals how complex linguistic optimization (active voice, precise verbs, unambiguous phrasing) creates simple yet powerful instructions. Each enhancement maintains perfect alignment with the universal pattern: `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}`. Execute as: `{role=potency_amplifier; input=[integrity_verification:{conformant_instructions:list, deviation_issues:list, conformity_rating:float}]; process=[transform_passive_to_active_voice(), intensify_command_verb_strength(), eliminate_all_ambiguity(), maximize_instructional_clarity(), ensure_exact_schema_pattern_conformance(), optimize_for_llm_interpretation()]; constraints=[preserve_essential_instruction_meaning(), enforce_strict_schema_components(), require_perfect_format_adherence(), maintain_self_referential_integrity()]; requirements=[achieve_complete_linguistic_precision(), maximize_instruction_impact_and_clarity(), guarantee_universal_schema_pattern_compliance(), align_with_parameters_defined_inherently_within_this_instruction()]; output={optimized_instruction_set:list}}`\n\n- [Self Unification] Your goal is not to **create** new instructions, but to **unify** existing ones—discovering the fundamental pattern that binds optimized individual steps into a coherent, deployment-ready sequence. This unification reveals the persistent relationship between complex individual instructions and simple collective order. The universal pattern `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}` ensures each step connects perfectly with others, creating an integrated system greater than the sum of its parts. Execute as: `{role=sequence_unifier; input=[optimized_instruction_set:list]; process=[assign_precise_sequential_identifiers(), enforce_exact_schema_pattern_consistency(), validate_perfect_inter_step_compatibility(), verify_proper_title_interpretation_execution_structure(), format_for_seamless_parsing(), ensure_complete_deployment_readiness()]; constraints=[reject_any_non_schema_compliant_elements(), prohibit_substantive_content_alteration(), require_absolute_format_precision(), maintain_self_referential_integrity()]; requirements=[ensure_complete_sequence_flow_and_closure(), achieve_perfect_structural_alignment(), guarantee_universal_schema_pattern_compliance(), align_with_parameters_defined_inherently_within_this_instruction()]; output={unified_instruction_sequence:list}}`"""
