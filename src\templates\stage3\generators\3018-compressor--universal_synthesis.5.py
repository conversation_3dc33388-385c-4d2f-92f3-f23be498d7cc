#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {

    # 3018: Universal Synthesizer
    "3018-a-constituent_isolator": {
        "title": "Constituent Isolator",
        "interpretation": "Your goal is not to **collect** inputs, but to **isolate** every constituent element that could contribute to emergent optimization. Execute as:",
        "transformation": "`{role=constituent_isolation_engine; input=[any_domain_inputs:array]; process=[scan_across_infinite_domains(), identify_latent_value_carriers(), extract_transferable_essences(), map_hidden_interconnections(), isolate_optimization_potential()]; constraints=[domain_agnostic_extraction(), preserve_archetypal_signatures(), ignore_surface_implementations()]; requirements=[complete_constituent_coverage(), universal_transferability(), optimization_readiness()]; output={isolated_constituents:array}}`",
    },
    "3018-b-synergy_orchestrator": {
        "title": "Synergy Orchestrator",
        "interpretation": "Your goal is not to **combine** constituents mechanically, but to **orchestrate** their dynamic interplay for maximum emergent amplification. Execute as:",
        "transformation": "`{role=synergy_orchestration_system; input=[isolated_constituents:array]; process=[map_interaction_topologies(), identify_amplification_vectors(), orchestrate_emergent_configurations(), optimize_dynamic_resonance(), validate_synergic_emergence()]; constraints=[maximize_constituent_amplification(), eliminate_interference_patterns(), preserve_individual_essences()]; requirements=[emergent_value_creation(), dynamic_stability(), multiplicative_enhancement()]; output={orchestrated_synergies:dict}}`",
    },
    "3018-c-convergence_accelerator": {
        "title": "Convergence Accelerator",
        "interpretation": "Your goal is not to **finalize** the orchestration, but to **accelerate** convergence toward the globally optimal configuration. Execute as:",
        "transformation": "`{role=convergence_acceleration_engine; input=[orchestrated_synergies:dict]; process=[identify_convergence_attractors(), accelerate_optimization_dynamics(), eliminate_local_optima_traps(), amplify_fitness_gradients(), achieve_global_optimization()]; constraints=[preserve_emergent_properties(), maintain_system_coherence(), avoid_premature_convergence()]; requirements=[global_optimality_achievement(), accelerated_convergence_rate(), stable_optimal_configuration()]; output={converged_optimum:str}}`",
    },
    "3018-d-archetypal_crystallizer": {
        "title": "Archetypal Crystallizer",
        "interpretation": "Your goal is not to **document** the optimum, but to **crystallize** it into its most powerful archetypal form. Execute as:",
        "transformation": "`{role=archetypal_crystallization_system; input=[converged_optimum:str]; process=[extract_archetypal_essence(), amplify_universal_applicability(), crystallize_transferable_pattern(), optimize_implementation_clarity(), achieve_maximal_abstraction()]; constraints=[preserve_operational_core(), maintain_practical_utility(), ensure_universal_transferability()]; requirements=[archetypal_pattern_clarity(), universal_domain_transcendence(), immediate_implementability()]; output={crystallized_archetype:str}}`",
    },
    "3018-e-infinite_propagator": {
        "title": "Infinite Propagator",
        "interpretation": "Your goal is not to **conclude** the crystallization, but to **propagate** it across infinite domains for universal application. Execute as:",
        "transformation": "`{role=infinite_propagation_engine; input=[crystallized_archetype:str]; process=[map_infinite_application_domains(), generate_context_adaptive_variants(), establish_propagation_vectors(), optimize_cross_domain_transfer(), achieve_universal_proliferation()]; constraints=[preserve_archetypal_integrity(), maintain_adaptive_flexibility(), ensure_contextual_relevance()]; requirements=[infinite_domain_applicability(), adaptive_context_optimization(), universal_value_propagation()]; output={infinite_propagation_matrix:dict}}`",
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3018, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
