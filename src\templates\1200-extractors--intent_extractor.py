#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 1200: Intent Extractor
    "1200-a-intent_extractor": {
        "title": "Intent Extractor",
        "interpretation": "Your goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:",
        "transformation": "`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
        # "context": {},
    },
}


def main():
    """Main execution function."""
    generator = BaseGenerator(
        generator_range=(1200, 1199),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
