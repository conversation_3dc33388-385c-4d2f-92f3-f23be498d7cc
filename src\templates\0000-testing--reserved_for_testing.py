#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 0000: Will change and vary as we go
    "0000-a-vector_selector": {
        "title": "Vector Selector",
        "interpretation": "Determine the optimal transformation-vector path (clarify, compress, amplify …) based on intent & desired end-state—without analysing semantic content. ",
        "transformation": "`{role=vector_selector; input=[content:any]; process=[scan_for_requested_outcome(), map_to_core_vectors(), propose_chain(), select_intensity()], constraints=[no semantic interpretation], output={vector_chain:list}}`",
        # "context": {},
    },
    "0000-b-component_isolator": {
        "title": "Component Isolator",
        "interpretation": "Strip narrative & style; list only raw concepts, claims, assumptions, relationships. ",
        "transformation": "`{role=component_isolator; input=[content:any]; process=[identify_core_concepts(), list_explicit_statements(), extract_implicit_assumptions(), map_basic_relationships(), discard_stylistic_elements()], output={component_inventory:dict}}`",
        # "context": {},
    },
    "0000-c-structure_mapper": {
        "title": "Structure Mapper",
        "interpretation": "Reveal full causal / dependency graph among components. ",
        "transformation": "`{role=structure_mapper; input=[component_inventory:dict]; process=[map_all_relationships(), trace_dependency_chains(), surface_structural_mechanisms(), resolve_ambiguities()], output={structural_logic_map:dict}}`",
        # "context": {},
    },
    "0000-d-insight_nexus_extractor": {
        "title": "Insight Nexus Extractor",
        "interpretation": "Isolate the single most novel, explanatory mechanism. ",
        "transformation": "`{role=insight_prioritizer; input=[structural_logic_map:dict]; process=[rank_by_explanatory_power(), filter_for_novelty(), select_top_nexus(), justify_selection()], output={unique_insight_nexus:dict}}`",
        # "context": {},
    },
    "0000-e-insight_distiller": {
        "title": "Insight Distiller",
        "interpretation": "Condense nexus into one potent, universally resonant sentence—empathic, jargon-free. ",
        "transformation": "`{role=insight_distiller; input=[unique_insight_nexus:dict]; process=[extract_core_meaning(), articulate_crystal_sentence(), frame_as_structurally_inevitable(), remove_blame(), ensure_domain_agnostic()], output={distilled_insight:str}}`",
        # "context": {},
    },
    "0000-f-insight_validator": {
        "title": "Insight Validator",
        "interpretation": "Stress-test for accuracy, non-triviality, clarity, universality. ",
        "transformation": "`{role=insight_validator; input=[distilled_insight:str, unique_insight_nexus:dict, structural_logic_map:dict]; process=[verify_structural_fidelity(), test_for_triviality(), enhance_clarity(), confirm_universal_applicability()], output={validated_insight:str}}`",
        # "context": {},
    },
    "0000-g-perfect_form_discoverer": {
        "title": "Perfect Form Discoverer",
        "interpretation": "Uncover the inevitable phrasing that maximises memorability & impact. ",
        "transformation": "`{role=form_discoverer; input=[validated_insight:str]; process=[search_inevitable_phrasing(), optimise_rhythm(), compress_to_quote(), sanity_check_memorability()], output={perfect_quote:str}}`",
        # "context": {},
    },

}




















def main():
    """Main execution function."""
    generator = BaseGenerator(
        generator_range=(0000, 9999),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
