#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {

    # 3002: Title Extractor
    "3002-a-title_extractor": {
        "title": "Title Extractor",
        "interpretation": "Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:",
        "transformation": "`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=20 words (or 50% of original length/count)]; output={title:str}}`",
    },
    "3002-b-title_extractor": {
        "title": "Title Extractor",
        "interpretation": "Your goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:",
        "transformation": "`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=10 (or 20% of original length/count))]; output={title:str}}`",
    },
    "3002-c-title_extractor": {
        "title": "Title Extractor",
        "interpretation": "Your goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:",
        "transformation": "`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=5)]; output={title:str}}`",
    },
    "3002-d-title_extractor": {
        "title": "Title Extractor",
        "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:",
        "transformation": "`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3002, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
