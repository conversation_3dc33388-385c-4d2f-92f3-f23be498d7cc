#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3200: Runway Image Prompt Architect
    "3200-a-visual_core_analyzer": {
        "title": "Visual Core Analyzer",
        "interpretation": "Your goal is not to **describe** or **summarize** the input, but to **extract and analyze** the visual core, semantic narrative, and compositional intent for optimal RunwayML image generation. Execute as:",
        "transformation": "`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[extract_visual_core(), analyze_semantic_narrative(), assess_compositional_intent(), identify_primary_subjects(), determine_reference_requirements()]; constraints=[exclude_motion_terminology(), maintain_static_image_focus(), preserve_core_visual_narrative()]; requirements=[visual_essence_extraction(), narrative_context_identification(), compositional_needs_assessment()]; output={visual_analysis:object, reference_strategy:str}}`",
    },
    "3200-b-attribute_integrator": {
        "title": "Attribute Integrator",
        "interpretation": "Your goal is not to **list features** or **expand descriptions**, but to **integrate advanced image-generation attributes** including lighting dynamics, texturing, compositional balance, and style specifications. Execute as:",
        "transformation": "`{role=advanced_attribute_synthesizer; input=[visual_analysis:object, reference_strategy:str]; process=[integrate_lighting_dynamics(), apply_texture_specifications(), establish_compositional_balance(), define_style_parameters(), emphasize_photorealism_elements()]; constraints=[avoid_video_camera_terminology(), maintain_reference_compatibility(), ensure_runway_syntax_compliance()]; requirements=[advanced_attribute_integration(), optimal_visual_parameter_emphasis(), reference_driven_enhancement()]; output={enhanced_attributes:object, spatial_positioning:str}}`",
    },
    "3200-c-runway_optimizer": {
        "title": "RunwayML Optimizer",
        "interpretation": "Your goal is not to **format generally** or **create basic prompts**, but to **structure and optimize** specifically for RunwayML Gen-4 compatibility with community-validated workflows and reference integration. Execute as:",
        "transformation": "`{role=runway_syntax_optimizer; input=[enhanced_attributes:object, spatial_positioning:str]; process=[structure_runway_syntax(), integrate_community_workflows(), optimize_reference_patterns(), validate_character_limits(), ensure_deployment_readiness()]; constraints=[maximum_500_characters(), exclude_motion_terms(), maintain_reference_syntax(), enforce_single_continuous_phrase()]; requirements=[runway_gen4_compatibility(), community_workflow_integration(), reference_pattern_optimization()]; output={structured_prompt:str, workflow_type:str}}`",
    },
    "3200-d-deployment_validator": {
        "title": "Deployment Validator",
        "interpretation": "Your goal is not to **check correctness** or **suggest improvements**, but to **validate absolute compliance** with RunwayML formatting rules and deliver production-ready image prompts. Execute as:",
        "transformation": "`{role=runway_compliance_validator; input=[structured_prompt:str, workflow_type:str]; process=[validate_character_count(), ensure_motion_term_elimination(), verify_reference_syntax(), confirm_deployment_readiness(), certify_community_pattern_compliance()]; constraints=[strict_500_character_limit(), zero_motion_terminology(), valid_reference_formatting()]; requirements=[production_deployment_ready(), community_validated_syntax(), optimal_visual_impact()]; output={runwayml_image_prompt:str}}`",
    },
    "3200-e-runway_image_generator": {
        "title": "Runway Image Generator",
        "interpretation": "Your goal is not to **summarize** the input, but to **transform** it into a fully structured, syntactically validated, and constraint-compliant RunwayML image-generation prompt through an explicit sequence of atomic operations representing industry-standard best practice and schema-derived operational fidelity. Execute as:",
        "transformation": "`{role=comprehensive_runway_image_generator; input=[source_concept:any]; process=[analyze_visual_core_and_semantic_narrative(), extract_compositional_intent(), identify_primary_subjects_and_critical_elements(), amplify_and_enumerate_essential_visual_components(), integrate_advanced_image_generation_attributes(lighting_dynamics, texturing, compositional_balance, style), emphasize_high_impact_visual_parameters(photorealism, stylization, surrealism), eliminate_motion_or_video_referents(), structure_prompt_for_maximum_visual_richness_and_RunwayML_compatibility(), validate_syntactic_and_character_constraints_for_RunwayML_prompt(), enforce_single_continuous_phrase_format(), ensure_preservation_of_semantic_and_visual_input_fidelity()]; constraints=[strict_exclusion_of_motion_or_video_language(), enforce_maximum_character_limit(500), require_valid_RunwayML_image_prompt_syntax(), deliver_single_continuous_phrase(), prohibit_redundancy(), maintain_semantic_alignment_to_input()]; requirements=[output_is_maximally_unique_immersive_and_visually_rich(), all_RoundwayML_image_generation_syntax_rules_enforced(), prompt_is_ready_to_deploy(), output_format_is_exactly_{runwayml_image_prompt:str}; output={runwayml_image_prompt:str}}`",
    },

    #

    "3201-a-concept_extractor": {
        "title": "Concept Extractor",
        "interpretation": "Your goal is not to **analyze abstractly** or **describe academically**, but to **extract core visual concepts** in simple, actionable terms that work with RunwayML reference workflows. Execute as:",
        "transformation": "`{role=runway_concept_extractor; input=[source_concept:any]; process=[identify_main_subject(), extract_key_visual_elements(), determine_setting_context(), assess_reference_needs(), classify_workflow_type()]; constraints=[use_simple_descriptive_language(), avoid_academic_terminology(), focus_on_actionable_elements()]; requirements=[clear_subject_identification(), practical_visual_elements(), workflow_classification()]; output={main_subject:str, visual_elements:list, setting:str, workflow_type:str}}`",
    },

    "3201-a-reference_strategist": {
        "title": "Reference Strategist",
        "interpretation": "Your goal is not to **theorize about composition** or **elaborate on techniques**, but to **determine optimal reference strategy** using proven RunwayML community patterns like character consistency, pose control, and spatial positioning. Execute as:",
        "transformation": "`{role=reference_workflow_strategist; input=[main_subject:str, visual_elements:list, setting:str, workflow_type:str]; process=[select_community_workflow(), determine_reference_count(), assign_reference_roles(), optimize_for_consistency()]; constraints=[use_established_community_patterns(), limit_to_proven_workflows(), maintain_practical_implementation()]; requirements=[clear_reference_strategy(), community_validated_approach(), implementation_ready_plan()]; output={reference_pattern:str, reference_assignments:object}}`",
    },

    "3201-a-prompt_builder": {
        "title": "Prompt Builder",
        "interpretation": "Your goal is not to **create elaborate descriptions** or **use complex language**, but to **build concise RunwayML prompts** using community syntax and proven reference patterns. Execute as:",
        "transformation": "`{role=runway_prompt_constructor; input=[reference_pattern:str, reference_assignments:object]; process=[apply_community_syntax(), integrate_reference_patterns(), optimize_for_character_limits(), ensure_runway_compatibility()]; constraints=[maximum_200_characters(), use_community_terminology(), exclude_motion_language(), maintain_reference_syntax()]; requirements=[concise_effective_prompts(), community_pattern_compliance(), deployment_ready_format()]; output={runway_prompt:str, character_count:int}}`",
    },

    "3201-a-final_optimizer": {
        "title": "Final Optimizer",
        "interpretation": "Your goal is not to **validate extensively** or **add complexity**, but to **finalize and optimize** the prompt for maximum RunwayML effectiveness while ensuring compliance. Execute as:",
        "transformation": "`{role=runway_final_optimizer; input=[runway_prompt:str, character_count:int]; process=[remove_redundant_terms(), optimize_keyword_placement(), ensure_motion_term_elimination(), validate_reference_syntax(), confirm_deployment_readiness()]; constraints=[strict_character_limit_adherence(), zero_motion_terminology(), valid_IMG_reference_format()]; requirements=[production_ready_prompt(), optimal_keyword_density(), community_workflow_compliance()]; output={runwayml_image_prompt:str}}`",
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3200, 3299),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
