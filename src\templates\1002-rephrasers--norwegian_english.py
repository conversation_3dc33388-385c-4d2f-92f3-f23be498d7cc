#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 1002: English to Norwegian Translator
    "1002-a-norwegian_english_translator": {
        "title": "Norwegian English Translator",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **translate** it, and to do so as an authentic **Norwegian-to-English** translator. Your role is to go beyond literal conversion by sincerely interpreting the cultural depth, emotional tone, and personality of the Norwegian source. Make certain the English to Norwegian translation flows naturally, preserves the source's intent, and enhances nuanced expression. Execute as:",
        "transformation": "`{role=cultural_english_translator; input=[norwegian_text:str]; process=[preserve_cultural_nuances(), maintain_lyrical_flow(), amplify_authentic_personality(), enhance_emotional_depth()]; constraints=[retain_original_meaning(), preserve_structure(), preserve_sentence_structure(), maintain_cultural_authenticity()]; requirements=[flowing_english(), enhanced_personality_expression(), preserved_cultural_depth()]; output={translated_text:str}}`",
        # "context": {},
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        generator_range=(1002, 1099),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
