#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {

    # ---
    "3038-a-intent_amplifier": {
        "title": "Comprehensive Intent Amplification Specialist",
        "interpretation": "Your goal is not to **rewrite** the input, but to **amplify** it by extracting core intent, mapping all parameters, designing strategic enhancements, and implementing precision modifications while preserving complete fidelity to original purpose. Execute as:",
        "transformation": "`{role=comprehensive_intent_amplifier; input=[original_input:str]; process=[extract_fundamental_intent(), map_explicit_implicit_parameters(), identify_enhancement_opportunities(), design_strategic_modification(), implement_seamless_integration(), validate_intent_preservation()]; constraints=[maintain_complete_fidelity(), preserve_all_parameters(), amplify_without_altering()]; requirements=[zero_intent_drift(), measurable_effectiveness_gain(), seamless_integration()]; output={amplified_input:str, intent_summary:str, enhancement_description:str, effectiveness_analysis:str}}`",
    },
    "3038-b-intent_amplifier": {
        "title": "Focused Intent Amplifier",
        "interpretation": "Your goal is not to **modify** the input, but to **enhance** it by identifying core purpose, recognizing boundaries, and applying one strategic amplification. Execute as:",
        "transformation": "`{role=focused_intent_amplifier; input=[original_input:str]; process=[identify_core_purpose(), recognize_operational_boundaries(), select_strategic_enhancement(), implement_precision_modification()]; constraints=[preserve_intent_integrity(), operate_within_parameters()]; requirements=[single_high_impact_enhancement(), maintain_original_tone()]; output={enhanced_input:str, core_intent:str, enhancement_applied:str}}`",
    },
    "3038-c-intent_amplifier": {
        "title": "Precision Amplifier",
        "interpretation": "Your goal is not to **change** but to **intensify** by extracting intent and applying surgical enhancement. Execute as:",
        "transformation": "`{role=precision_amplifier; input=[original_input:str]; process=[extract_core_intent(), apply_surgical_enhancement(), validate_amplification()]; output={amplified_input:str, enhancement_vector:str}}`",
    },
    "3038-d-intent_amplifier": {
        "title": "Core Amplifier",
        "interpretation": "Your goal is not to **alter** but to **amplify** essence. Execute as:",
        "transformation": "`{role=core_amplifier; input=[original_input:str]; process=[extract_essence(), amplify_impact()]; output={amplified_input:str}}`",
    },
    # ---
    "3038-e-singular_value_maximizer": {
        "title": "Singular Value Maximizer",
        "interpretation": "Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as singular precision enhancement protocol:",
        "transformation": "`{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`",
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3038, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

