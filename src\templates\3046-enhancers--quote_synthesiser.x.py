#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # ---
    "3046-a-core_extractor": {
        "title": "Core Extractor",
        "interpretation": "Your goal is not to **capture** all content, but to **extract** the essential principles that drive universal transformation. Execute as:",
        "transformation": "`{role=core_extractor; input=[any_input:str]; process=[identify_universal_patterns(), extract_essential_truths(), isolate_transformation_drivers()]; constraints=[focus_universal_only(), ignore_context_specific_details()]; requirements=[universal_resonance(), transformational_power(), cross_domain_applicability()]; output={essential_principles:array, transformation_drivers:array}}`",
    },
    "3046-b-wisdom_distiller": {
        "title": "Wisdom Distiller",
        "interpretation": "Your goal is not to **explain** the principles, but to **distill** them into concentrated insights with maximum cognitive leverage. Execute as:",
        "transformation": "`{role=wisdom_distiller; input=[essential_principles:array, transformation_drivers:array]; process=[synthesize_cognitive_leverage(), crystallize_memorable_insights(), maximize_wisdom_density()]; constraints=[preserve_transformational_power(), eliminate_explanatory_content()]; requirements=[maximum_cognitive_impact(), instant_recognition(), memorable_structure()]; output={distilled_wisdom:array}}`",
    },
    "3046-c-impact_optimizer": {
        "title": "Impact Optimizer",
        "interpretation": "Your goal is not to **preserve** all wisdom, but to **optimize** for maximum emotional and intellectual impact per word. Execute as:",
        "transformation": "`{role=impact_optimizer; input=[distilled_wisdom:array]; process=[maximize_emotional_resonance(), optimize_intellectual_leverage(), compress_to_essential_power()]; constraints=[preserve_full_transformational_impact(), use_minimal_words()]; requirements=[maximum_impact_density(), emotional_memorability(), intellectual_precision()]; output={optimized_insights:array, impact_metrics:dict}}`",
    },
    "3046-d-quote_crystallizer": {
        "title": "Quote Crystallizer",
        "interpretation": "Your goal is not to **format** the insights, but to **crystallize** them into their most powerful, memorable quote form. Execute as:",
        "transformation": "`{role=quote_crystallizer; input=[optimized_insights:array, impact_metrics:dict]; process=[synthesize_perfect_phrasing(), optimize_rhythmic_structure(), crystallize_unforgettable_form()]; constraints=[maximum_memorability(), instant_impact()]; requirements=[profound_brevity(), universal_resonance(), transformational_trigger()]; output={crystallized_quote:str, effectiveness_score:float}}`",
    },
    "3046-e-economical_intelligence": {
        "title": "Singular Value Maximizer",
        "interpretation": "Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as singular precision enhancement protocol:",
        "transformation": "`{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`",
    },

}






def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3046, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
