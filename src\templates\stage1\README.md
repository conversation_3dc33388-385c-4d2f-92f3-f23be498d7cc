# Stage 1: Prototyping & Testing

## Overview
Rapid iteration and experimentation with auto-generated IDs

**ID Range**: 1000-1999  
**Auto-ID**: Yes

## Workflow
1. Get next available auto-ID
2. Create template with generated ID
3. Test and iterate rapidly
4. No manual ID management required

## Directory Structure
```
stage1/
├── generators/          # Template definition files
├── md/                 # Generated markdown templates  
├── catalog.json        # Stage-specific catalog
└── README.md          # This file
```

## Usage
```bash
# Generate templates for this stage
python stage1/generators/*.py

# Update stage catalog  
python core/catalog_generator.py --stage stage1

# Execute templates
python ../../lvl1_sequence_executor.py --sequence XXXX --prompt "Your input"
```
