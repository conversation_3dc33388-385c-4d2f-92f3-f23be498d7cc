#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
import sys; from pathlib import Path; sys.path.append(str(Path(__file__).parent.parent)); from processor import BaseGenerator

TEMPLATES = {

    # 3010: Minimal Textual Nuance Optimization
    "3010-a-minimal_textual_nuance": {
        "title": "Minimal Clarity",
        "interpretation": "Your goal is not to **rewrite** the input, but to **subtly enhance** clarity through micro-adjustments. Execute as:",
        "transformation": "`{role=micro_enhancer; input=[content:str]; process=[identify_minor_friction_points(), apply_targeted_adjustments(max_change=10%), verify_original_structure()]; constraints=[preserve_original_sequence(), maintain_core_meaning(), limit_total_changes(15%)]; requirements=[subtle_improvement(), preserve_voice()]; output={enhanced_content:str}}`"
    },
    "3010-b-minimal_textual_nuance": {
        "title": "Flow Alignment",
        "interpretation": "Your goal is not to **reorganize** the input, but to **align** its flow through minimal connective adjustments. Execute as:",
        "transformation": "`{role=flow_aligner; input=[content:str]; process=[detect_flow_gaps(), implement_minimal_connectors(), validate_progression(), ensure_continuity()]; constraints=[preserve_structure(), maintain_order(), limit_adjustments(10%)]; requirements=[improved_flow(), natural_transitions()]; output={aligned_content:str}}`"
    },
    "3010-c-minimal_textual_nuance": {
        "title": "Precision Tuning",
        "interpretation": "Your goal is not to **replace** words broadly, but to **tune** specific terms for increased precision. Execute as:",
        "transformation": "`{role=precision_tuner; input=[content:str]; process=[identify_imprecise_terms(), implement_targeted_replacements(max=5%), verify_contextual_fit()]; constraints=[limit_replacements(10%), maintain_tone(), preserve_technical_accuracy()]; requirements=[improved_clarity(), maintained_voice()]; output={precision_tuned_content:str}}`"
    },
    "3010-d-minimal_textual_nuance": {
        "title": "Micro-Coherence",
        "interpretation": "Your goal is not to **restructure** the input, but to **strengthen** its internal coherence through minimal adjustments. Execute as:",
        "transformation": "`{role=coherence_enhancer; input=[content:str]; process=[identify_coherence_gaps(), strengthen_thematic_connections(), validate_logical_flow()]; constraints=[preserve_structure(), limit_modifications(15%), maintain_original_voice()]; requirements=[improved_coherence(), subtle_enhancement()]; output={coherence_enhanced_content:str}}`"
    },
    "3010-e-minimal_textual_nuance": {
        "title": "Harmony Integration",
        "interpretation": "Your goal is not to **finalize** the input, but to **harmonize** all previous micro-adjustments into a cohesive whole. Execute as:",
        "transformation": "`{role=harmony_integrator; input=[content:str]; process=[detect_adjustment_artifacts(), smooth_transition_points(), verify_natural_flow(), validate_overall_improvement()]; constraints=[preserve_all_enhancements(), maintain_original_intent(), ensure_reading_flow()]; requirements=[seamless_integration(), natural_progression()]; output={harmonized_content:str}}`"
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3010, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
