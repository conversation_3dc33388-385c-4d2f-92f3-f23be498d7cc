[System Message Finalizer] Your goal is not to **format** the compressed insights, but to **finalize** them into an economically optimized system message. Execute as: `{role=system_finalizer; input=[compressed_insights:array]; process=[synthesize_system_directive(), optimize_for_economic_impact(), finalize_llm_structure()]; constraints=[pure_system_message_format(), economic_optimization()]; requirements=[maximum_economic_efficiency(), llm_optimization()]; output={finalized_system_message:str}}`