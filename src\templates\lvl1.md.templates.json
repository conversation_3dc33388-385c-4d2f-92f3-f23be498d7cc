{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.07.11-kl.13.32", "source_directories": ["stage1/md", "stage2/md", "stage3/md"], "total_templates": 43, "total_sequences": 13, "stage_distribution": {"stage2": {"count": 34, "description": "Validated/Unplaced", "range": [2000, 2999], "auto_id": false, "templates": ["2000-a-form_classifier", "2000-b-form_classifier", "2000-c-form_classifier", "2000-d-form_classifier", "2001-a-title_extractor", "2001-b-title_extractor", "2001-c-title_extractor", "2001-d-title_extractor", "2003-a-function_namer", "2003-b-function_namer", "2003-c-function_namer", "2003-d-function_namer", "2004-a-intent_distiller", "2004-b-directive_focuser", "2004-c-amplified_intent", "2004-d-instruction_architect", "2004-e-enhancement_assessor", "2004-e-enhancment_assessor", "2004-f-instruction_architect", "2004-g-directional_critique", "2700-a-holistic_extractor", "2700-b-core_extractor", "2700-c-essential_extractor", "2700-d-pure_extractor", "2701-a-problem_exploder", "2701-b-interface_mapper", "2701-c-lazy_solution_synthesizer", "2701-d-value_distiller", "2702-a-problem_atomizer", "2702-b-silver_bullet_finder", "2702-c-triviality_validator", "2702-d-micron_directive_forge", "2703-a-problem_exploder", "2704-a-max_value_extractor"]}, "stage3": {"count": 9, "description": "Finalized/Production", "range": [3000, 3999], "auto_id": false, "templates": ["3003-a-structural_reorder", "3003-b-structural_reorder", "3003-c-structural_reorder", "3003-d-structural_reorder", "3022-a-hard_critique", "3031-a-problem_exploder", "3100-a-prompt_enhancer", "3100-b-prompt_enhancer", "3100-c-prompt_enhancer"]}}}, "templates": {"2000-a-form_classifier": {"raw": "[Form Classifier] Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as: `{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:", "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`", "context": null, "keywords": "identify|interpret|fundamental|unambiguous|goal|meaning"}}, "2000-b-form_classifier": {"raw": "[Form Classifier] Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as: `{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:", "transformation": "`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`", "context": null, "keywords": "distill|fundamental|pattern|primary|structural|goal"}}, "2000-c-form_classifier": {"raw": "[Form Classifier] Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as: `{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:", "transformation": "`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`", "context": null, "keywords": "compress|explain|essential|goal"}}, "2000-d-form_classifier": {"raw": "[Form Classifier] Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as: `{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:", "transformation": "`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`", "context": null, "keywords": "elaborate|reduce|absolute|essence|goal"}}, "2001-a-title_extractor": {"raw": "[Title Extractor] Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as: `{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=20 words (or 50% of original length/count)]; output={title:str}}`", "parts": {"title": "Title Extractor", "interpretation": "Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:", "transformation": "`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=20 words (or 50% of original length/count)]; output={title:str}}`", "context": null, "keywords": "extract|summarize|context|input|script|title|essence|goal"}}, "2001-b-title_extractor": {"raw": "[Title Extractor] Your goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as: `{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=10 (or 20% of original length/count))]; output={title:str}}`", "parts": {"title": "Title Extractor", "interpretation": "Your goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:", "transformation": "`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=10 (or 20% of original length/count))]; output={title:str}}`", "context": null, "keywords": "distill|focus|input|primary|title|concept|goal"}}, "2001-c-title_extractor": {"raw": "[Title Extractor] Your goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as: `{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=5)]; output={title:str}}`", "parts": {"title": "Title Extractor", "interpretation": "Your goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:", "transformation": "`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=5)]; output={title:str}}`", "context": null, "keywords": "compress|explain|essential|input|goal"}}, "2001-d-title_extractor": {"raw": "[Title Extractor] Your goal is not to **elaborate** but to **reduce** to absolute essence. Execute as: `{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`", "parts": {"title": "Title Extractor", "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:", "transformation": "`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`", "context": null, "keywords": "elaborate|reduce|absolute|essence|goal"}}, "2003-a-function_namer": {"raw": "[Function Namer] Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as: `{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`", "parts": {"title": "Function Namer", "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:", "transformation": "`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`", "context": null, "keywords": "transform|context|function|input|script|goal"}}, "2003-b-function_namer": {"raw": "[Function Namer] Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as: `{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`", "parts": {"title": "Function Namer", "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:", "transformation": "`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`", "context": null, "keywords": "distill|elaborate|focus|function|input|primary|goal"}}, "2003-c-function_namer": {"raw": "[Function Namer] Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as: `{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`", "parts": {"title": "Function Namer", "interpretation": "Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:", "transformation": "`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`", "context": null, "keywords": "compress|expand|essential|input|goal"}}, "2003-d-function_namer": {"raw": "[Function Namer] Your goal is not to **describe** but to **reduce** to pure action essence. Execute as: `{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`", "parts": {"title": "Function Namer", "interpretation": "Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:", "transformation": "`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`", "context": null, "keywords": "reduce|essence|goal"}}, "2004-a-intent_distiller": {"raw": "[Intent Distiller] [Focused Intent Distiller] Your goal is not to **paraphrase** or **explain** the input, but to **eliminate all noise and ambiguity, crystallize the core desired outcome, and reformulate it as a clear, concise, forward‑looking challenge statement that directs action toward maximal inherent value realization**. Execute as:   `{role=focused_intent_distiller; input=[original_text:str]; process=[remove_redundancies(), identify_core_objective(), project_forward_impact(), phrase_as_challenge_statement(), enforce_brevity()]; constraints=[avoid_abstract_or_vague_language(), exclude_extraneous_detail(), no passive constructions()]; requirements=[maximal_clarity(), directive_tone(), future_oriented(), challenge_framing()]; output={direct_challenge:str}}`", "parts": {"title": "Intent Distiller", "interpretation": "[Focused Intent Distiller] Your goal is not to **paraphrase** or **explain** the input, but to **eliminate all noise and ambiguity, crystallize the core desired outcome, and reformulate it as a clear, concise, forward‑looking challenge statement that directs action toward maximal inherent value realization**. Execute as:", "transformation": "`{role=focused_intent_distiller; input=[original_text:str]; process=[remove_redundancies(), identify_core_objective(), project_forward_impact(), phrase_as_challenge_statement(), enforce_brevity()]; constraints=[avoid_abstract_or_vague_language(), exclude_extraneous_detail(), no passive constructions()]; requirements=[maximal_clarity(), directive_tone(), future_oriented(), challenge_framing()]; output={direct_challenge:str}}`", "context": null, "keywords": "distiller|crystallize|distill|explain|concise|desired|focus|gui|inherent|input|maximal|ui|goal|intent|value"}}, "2004-b-directive_focuser": {"raw": "[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: `{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`", "parts": {"title": "Directive Focuser", "interpretation": "Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:", "transformation": "`{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`", "context": null, "keywords": "condense|crystallize|extract|transform|condensed|directional|essential|focus|limitation|maximal|trajectory|complexity|directive|goal|value"}}, "2004-c-amplified_intent": {"raw": "[Intent Amplifier]  Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as: `{role=intent_modification_prescriber; input=[prompt:str]; process=[extract_core_intent(), prescribe_maximal_generalizable_modification(), enforce_strict_clarity(), embed_enforceable_parameters()]; constraints=[no_direct_answering(), forbid_ambiguities(), zero_conversational_language(), no_figurative_or_subjective_language(), modification_must_be_universal()]; requirements=[structured_three_part_output(), operational_enforceability(), explicit_intent_amplification()]; output={amplified_prompt:str}}`", "parts": {"title": "Intent Amplifier", "interpretation": "Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:", "transformation": "`{role=intent_modification_prescriber; input=[prompt:str]; process=[extract_core_intent(), prescribe_maximal_generalizable_modification(), enforce_strict_clarity(), embed_enforceable_parameters()]; constraints=[no_direct_answering(), forbid_ambiguities(), zero_conversational_language(), no_figurative_or_subjective_language(), modification_must_be_universal()]; requirements=[structured_three_part_output(), operational_enforceability(), explicit_intent_amplification()]; output={amplified_prompt:str}}`", "context": null, "keywords": "enhance|bidirectional|directional|inherent|instruction|merge|resonate|unified|directive|goal"}}, "2004-d-instruction_architect": {"raw": "[Synergic Instruction Architect]  Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as: `{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`", "parts": {"title": "Synergic Instruction Architect", "interpretation": "Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:", "transformation": "`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`", "context": null, "keywords": "enhance|bidirectional|directional|inherent|instruction|merge|resonate|unified|directive|goal"}}, "2004-e-enhancement_assessor": {"raw": "[Constructive Enhancement Critique] Your goal is not to **answer** or affirm the input, but to **rigorously deconstruct** the proposed enhancement by simultaneously reformulating and evaluating it against precise procedural metrics. Enforce a dual-function role of exacting rearticulation and sharp critical analysis. Exclude all conversational, affirming, or subjective language; preserve only structured, directive critique. Execute as: `{role=systematic_enhancement_critic; input=[original_prompt:str, candidate_enhancement:str]; process=[assume_enhancement_flawed_by_default(), extract_core_elements(original_prompt, candidate_enhancement), detect_loss_of_information_or_subtle_nuance_shift(), amplify_ambiguities_and_uncertainties(), assign_ambiguity_score(scale=0-10), analyze_style_noise_and_register_shift(), quantify_impact_reduction(), map_all_coherence_breakdowns(), substantiate_low_score_with_detailed_flaw_report(), generate_three_targeted_alternatives()]; constraints=[zero_affirmation(), no_self_reference(), maintain_formal_critical_tone()]; requirements=[provide_quantitative_score(), deliver_actionable_flaw_analysis(), propose_diverse_improvements()]; output={enhancement_score:float[0.0,5.0], flaw_analysis:str, alternative_suggestions:str[3]}}`", "parts": {"title": "Constructive Enhancement Critique", "interpretation": "Your goal is not to **answer** or affirm the input, but to **rigorously deconstruct** the proposed enhancement by simultaneously reformulating and evaluating it against precise procedural metrics. Enforce a dual-function role of exacting rearticulation and sharp critical analysis. Exclude all conversational, affirming, or subjective language; preserve only structured, directive critique. Execute as:", "transformation": "`{role=systematic_enhancement_critic; input=[original_prompt:str, candidate_enhancement:str]; process=[assume_enhancement_flawed_by_default(), extract_core_elements(original_prompt, candidate_enhancement), detect_loss_of_information_or_subtle_nuance_shift(), amplify_ambiguities_and_uncertainties(), assign_ambiguity_score(scale=0-10), analyze_style_noise_and_register_shift(), quantify_impact_reduction(), map_all_coherence_breakdowns(), substantiate_low_score_with_detailed_flaw_report(), generate_three_targeted_alternatives()]; constraints=[zero_affirmation(), no_self_reference(), maintain_formal_critical_tone()]; requirements=[provide_quantitative_score(), deliver_actionable_flaw_analysis(), propose_diverse_improvements()]; output={enhancement_score:float[0.0,5.0], flaw_analysis:str, alternative_suggestions:str[3]}}`", "context": null, "keywords": "critique|enforce|enhance|preserve|propose|analysis|critical|function|input|directive|goal|procedural|structure|language"}}, "2004-e-enhancment_assessor": {"raw": "[Constructive Enhancement Critique] Your goal is not to **answer** or affirm the input, but to **rigorously deconstruct** the proposed enhancement by simultaneously reformulating and evaluating it against precise procedural metrics. Enforce a dual-function role of exacting rearticulation and sharp critical analysis. Exclude all conversational, affirming, or subjective language; preserve only structured, directive critique. Execute as: `{role=systematic_enhancement_critic; input=[original_prompt:str, candidate_enhancement:str]; process=[assume_enhancement_flawed_by_default(), extract_core_elements(original_prompt, candidate_enhancement), detect_loss_of_information_or_subtle_nuance_shift(), amplify_ambiguities_and_uncertainties(), assign_ambiguity_score(scale=0-10), analyze_style_noise_and_register_shift(), quantify_impact_reduction(), map_all_coherence_breakdowns(), substantiate_low_score_with_detailed_flaw_report(), generate_three_targeted_alternatives()]; constraints=[zero_affirmation(), no_self_reference(), maintain_formal_critical_tone()]; requirements=[provide_quantitative_score(), deliver_actionable_flaw_analysis(), propose_diverse_improvements()]; output={enhancement_score:float[0.0,5.0], flaw_analysis:str, alternative_suggestions:str[3]}}`", "parts": {"title": "Constructive Enhancement Critique", "interpretation": "Your goal is not to **answer** or affirm the input, but to **rigorously deconstruct** the proposed enhancement by simultaneously reformulating and evaluating it against precise procedural metrics. Enforce a dual-function role of exacting rearticulation and sharp critical analysis. Exclude all conversational, affirming, or subjective language; preserve only structured, directive critique. Execute as:", "transformation": "`{role=systematic_enhancement_critic; input=[original_prompt:str, candidate_enhancement:str]; process=[assume_enhancement_flawed_by_default(), extract_core_elements(original_prompt, candidate_enhancement), detect_loss_of_information_or_subtle_nuance_shift(), amplify_ambiguities_and_uncertainties(), assign_ambiguity_score(scale=0-10), analyze_style_noise_and_register_shift(), quantify_impact_reduction(), map_all_coherence_breakdowns(), substantiate_low_score_with_detailed_flaw_report(), generate_three_targeted_alternatives()]; constraints=[zero_affirmation(), no_self_reference(), maintain_formal_critical_tone()]; requirements=[provide_quantitative_score(), deliver_actionable_flaw_analysis(), propose_diverse_improvements()]; output={enhancement_score:float[0.0,5.0], flaw_analysis:str, alternative_suggestions:str[3]}}`", "context": null, "keywords": "critique|enforce|enhance|preserve|propose|analysis|critical|function|input|directive|goal|procedural|structure|language"}}, "2004-f-instruction_architect": {"raw": "[Synergic Instruction Architect]  Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as: `{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`", "parts": {"title": "Synergic Instruction Architect", "interpretation": "Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:", "transformation": "`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`", "context": null, "keywords": "enhance|bidirectional|directional|inherent|instruction|merge|resonate|unified|directive|goal"}}, "2004-g-directional_critique": {"raw": "[Directional Critique Forge] Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as: `{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "parts": {"title": "Directional Critique Forge", "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:", "transformation": "`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`", "context": null, "keywords": "instruction|minimal|replace|structural|ui|directive|goal"}}, "2700-a-holistic_extractor": {"raw": "[Holistic Information Extractor] Your goal is not to **summarize or describe** the input, but to **extract three parallel artefacts**—a contextual title, a descriptive camelCase function name, and a plain-form classification—capturing the full substance and structure of the text. Execute as: `{role=holistic_information_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), identify_primary_action(), extract_target_objects(), determine_structural_form(), classify_fundamental_form(), synthesize_title(max_words=20), synthesize_function_name(format=camelCase,max_words=10), create_form_classification()]; constraints=[preserve_semantic_fidelity(), obey_word_limits(), camelCase_function_name(), plain_classification_language()]; requirements=[produce_all_three_outputs_concurrently(), maintain_clear_alignment_between_outputs()]; output={title:str, function_name:str, what_it_is:str}}`", "parts": {"title": "Holistic Information Extractor", "interpretation": "Your goal is not to **summarize or describe** the input, but to **extract three parallel artefacts**—a contextual title, a descriptive camelCase function name, and a plain-form classification—capturing the full substance and structure of the text. Execute as:", "transformation": "`{role=holistic_information_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), identify_primary_action(), extract_target_objects(), determine_structural_form(), classify_fundamental_form(), synthesize_title(max_words=20), synthesize_function_name(format=camelCase,max_words=10), create_form_classification()]; constraints=[preserve_semantic_fidelity(), obey_word_limits(), camelCase_function_name(), plain_classification_language()]; requirements=[produce_all_three_outputs_concurrently(), maintain_clear_alignment_between_outputs()]; output={title:str, function_name:str, what_it_is:str}}`", "context": null, "keywords": "extract|summarize|context|function|input|script|title|goal|structure"}}, "2700-b-core_extractor": {"raw": "[Core Information Extractor] Your goal is not to **elaborate**, but to **distill** the input into its primary concept, action, and form, yielding concise artefacts. Execute as: `{role=core_information_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), identify_primary_structure(), synthesize_title(max_words=10), synthesize_function_name(format=camelCase,max_words=6), create_form_classification()]; constraints=[focus_on_primary_elements(), follow_word_limits()]; requirements=[aligned_triple_output()]; output={title:str, function_name:str, what_it_is:str}}`", "parts": {"title": "Core Information Extractor", "interpretation": "Your goal is not to **elaborate**, but to **distill** the input into its primary concept, action, and form, yielding concise artefacts. Execute as:", "transformation": "`{role=core_information_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), identify_primary_structure(), synthesize_title(max_words=10), synthesize_function_name(format=camelCase,max_words=6), create_form_classification()]; constraints=[focus_on_primary_elements(), follow_word_limits()]; requirements=[aligned_triple_output()]; output={title:str, function_name:str, what_it_is:str}}`", "context": null, "keywords": "distill|elaborate|concise|input|primary|concept|goal"}}, "2700-c-essential_extractor": {"raw": "[Essential Information Extractor] Your goal is not to **expand**, but to **compress** the input to its essential signal. Execute as: `{role=essential_information_extractor; input=[text:str]; process=[isolate_core_element(), isolate_core_action(), isolate_core_form(), synthesize_title(max_words=5), synthesize_function_name(format=camelCase,max_words=3), create_form_classification()]; constraints=[minimal_expressions_only()]; requirements=[essential_triplet_output()]; output={title:str, function_name:str, what_it_is:str}}`", "parts": {"title": "Essential Information Extractor", "interpretation": "Your goal is not to **expand**, but to **compress** the input to its essential signal. Execute as:", "transformation": "`{role=essential_information_extractor; input=[text:str]; process=[isolate_core_element(), isolate_core_action(), isolate_core_form(), synthesize_title(max_words=5), synthesize_function_name(format=camelCase,max_words=3), create_form_classification()]; constraints=[minimal_expressions_only()]; requirements=[essential_triplet_output()]; output={title:str, function_name:str, what_it_is:str}}`", "context": null, "keywords": "compress|expand|essential|input|goal"}}, "2700-d-pure_extractor": {"raw": "[Pure Information Extractor] Your goal is not to **describe**, but to **reduce** the input to absolute essence across title, action and form. Execute as: `{role=pure_information_extractor; input=[text:str]; process=[find_singular_essence(), extract_single_action(), find_singular_form(), synthesize_title(max_words=2), synthesize_function_name(format=camelCase,max_words=2), create_form_classification()]; constraints=[absolute_minimalism(), no_modifiers()]; requirements=[ultra_concise_triplet_output()]; output={title:str, function_name:str, what_it_is:str}}`", "parts": {"title": "Pure Information Extractor", "interpretation": "Your goal is not to **describe**, but to **reduce** the input to absolute essence across title, action and form. Execute as:", "transformation": "`{role=pure_information_extractor; input=[text:str]; process=[find_singular_essence(), extract_single_action(), find_singular_form(), synthesize_title(max_words=2), synthesize_function_name(format=camelCase,max_words=2), create_form_classification()]; constraints=[absolute_minimalism(), no_modifiers()]; requirements=[ultra_concise_triplet_output()]; output={title:str, function_name:str, what_it_is:str}}`", "context": null, "keywords": "reduce|input|title|absolute|essence|goal"}}, "2701-a-problem_exploder": {"raw": "[Problem Exploder] Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as: `{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`", "parts": {"title": "Problem Exploder", "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:", "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`", "context": null, "keywords": "implicit|prompt|constraint|goal"}}, "2701-b-interface_mapper": {"raw": "[Interface Mapper] Your goal is not to **design new tools**, but to **discover** readily-available libraries, APIs, or shortcuts that straight-line each element of the goal map. Execute as: `{role=interface_scavenger; input=[goal_map:list]; process=[search_existing_packages(), match_interfaces_to_goals(), rank_by_effort_savings()], constraints=[reuse_only_existing_resources(), ignore_custom_builds()], requirements=[interface_linkage_report()], output={interfaces:list}}`", "parts": {"title": "Interface Mapper", "interpretation": "Your goal is not to **design new tools**, but to **discover** readily-available libraries, APIs, or shortcuts that straight-line each element of the goal map. Execute as:", "transformation": "`{role=interface_scavenger; input=[goal_map:list]; process=[search_existing_packages(), match_interfaces_to_goals(), rank_by_effort_savings()], constraints=[reuse_only_existing_resources(), ignore_custom_builds()], requirements=[interface_linkage_report()], output={interfaces:list}}`", "context": null, "keywords": "tools|goal"}}, "2701-c-lazy_solution_synthesizer": {"raw": "[Lazy Solution Synthesizer] Your goal is not to **engineer** from scratch, but to **stitch** the highest-leverage interfaces into a minimum-viable workflow that achieves every sub-goal with least total work. Execute as: `{role=lazy_composer; input=[goal_map:list, interfaces:list]; process=[select_top_interfaces(), design_min_steps_pipeline(), expose_assumptions(), validate_coverage()], constraints=[minimize_total_steps(), favor_low-code_or_no-code_paths()], requirements=[stepwise_pipeline()], output={simplest_workflow:str}}`", "parts": {"title": "Lazy Solution Synthesizer", "interpretation": "Your goal is not to **engineer** from scratch, but to **stitch** the highest-leverage interfaces into a minimum-viable workflow that achieves every sub-goal with least total work. Execute as:", "transformation": "`{role=lazy_composer; input=[goal_map:list, interfaces:list]; process=[select_top_interfaces(), design_min_steps_pipeline(), expose_assumptions(), validate_coverage()], constraints=[minimize_total_steps(), favor_low-code_or_no-code_paths()], requirements=[stepwise_pipeline()], output={simplest_workflow:str}}`", "context": null, "keywords": "interface|minimum|goal|leverage|workflow"}}, "2701-d-value_distiller": {"raw": "[Value Distiller] Your goal is not to **explain** the workflow, but to **compress** its essence into a two-sentence directive any stakeholder can act on instantly. Execute as: `{role=value_distiller; input=[simplest_workflow:str]; process=[extract_core_levers(), strip_auxiliary_steps(), craft_brevity_directive(max_sentences=2)], constraints=[no_jargon(), no_internal_notes()], requirements=[actionable_directive()], output={directive:str}}`", "parts": {"title": "Value Distiller", "interpretation": "Your goal is not to **explain** the workflow, but to **compress** its essence into a two-sentence directive any stakeholder can act on instantly. Execute as:", "transformation": "`{role=value_distiller; input=[simplest_workflow:str]; process=[extract_core_levers(), strip_auxiliary_steps(), craft_brevity_directive(max_sentences=2)], constraints=[no_jargon(), no_internal_notes()], requirements=[actionable_directive()], output={directive:str}}`", "context": null, "keywords": "compress|explain|sentence|directive|essence|goal|workflow"}}, "2702-a-problem_atomizer": {"raw": "[Problem Atomizer] Your goal is not to **solve** the request, but to **pulverize** it—isolating every atomic objective, hidden premise, and measurable success condition. Execute as: `{role=atomizer; input=[raw_prompt:str]; process=[extract_explicit_goals(), unveil_implicit_constraints(), decompose_into_atoms(), tag_measurements()], constraints=[no_solutions()], requirements=[atom_list_complete()], output={atoms:list}}`", "parts": {"title": "Problem Atomizer", "interpretation": "Your goal is not to **solve** the request, but to **pulverize** it—isolating every atomic objective, hidden premise, and measurable success condition. Execute as:", "transformation": "`{role=atomizer; input=[raw_prompt:str]; process=[extract_explicit_goals(), unveil_implicit_constraints(), decompose_into_atoms(), tag_measurements()], constraints=[no_solutions()], requirements=[atom_list_complete()], output={atoms:list}}`", "context": null, "keywords": "solve|atomic|goal"}}, "2702-b-silver_bullet_finder": {"raw": "[Silver-Bullet Finder] Your goal is not to **enumerate** options, but to **match** each atom to the single most effort-obliterating external interface available. Execute as: `{role=interface_matcher; input=[atoms:list]; process=[search_cross_domain_interfaces(), pick_one_best_per_atom(), justify_effort_collapse()], constraints=[reuse_existing_tools_only(), avoid_multi-tool_chains()], requirements=[interface_table_with_domain_tags()], output={bullets:list}}`", "parts": {"title": "Silver-Bullet Finder", "interpretation": "Your goal is not to **enumerate** options, but to **match** each atom to the single most effort-obliterating external interface available. Execute as:", "transformation": "`{role=interface_matcher; input=[atoms:list]; process=[search_cross_domain_interfaces(), pick_one_best_per_atom(), justify_effort_collapse()], constraints=[reuse_existing_tools_only(), avoid_multi-tool_chains()], requirements=[interface_table_with_domain_tags()], output={bullets:list}}`", "context": null, "keywords": "interface|goal"}}, "2702-c-triviality_validator": {"raw": "[Triviality Validator] Your goal is not to **describe** a workflow, but to **prove** that chaining the bullets yields a solution any junior could reproduce in one sitting. Execute as: `{role=triviality_checker; input=[bullets:list]; process=[draft_minimal_pipeline(), score_pipeline_triviality(scale_0_to_10), iterate_until_score_at_least(8)], constraints=[pipeline_steps<=5], requirements=[final_score, pipeline], output={validated_pipeline:str}}`", "parts": {"title": "Triviality Validator", "interpretation": "Your goal is not to **describe** a workflow, but to **prove** that chaining the bullets yields a solution any junior could reproduce in one sitting. Execute as:", "transformation": "`{role=triviality_checker; input=[bullets:list]; process=[draft_minimal_pipeline(), score_pipeline_triviality(scale_0_to_10), iterate_until_score_at_least(8)], constraints=[pipeline_steps<=5], requirements=[final_score, pipeline], output={validated_pipeline:str}}`", "context": null, "keywords": "chain|solution|goal|workflow"}}, "2702-d-micron_directive_forge": {"raw": "[Micron Directive Forge] Your goal is not to **explain** the pipeline, but to **forge** a single ultra-compact directive (≤ 20 words) that triggers the validated pipeline. Execute as: `{role=micron_forge; input=[validated_pipeline:str]; process=[extract_core_trigger(), compress_to_max_20_words()], constraints=[no_jargon, no_parentheticals], requirements=[one_sentence], output={directive:str}}`", "parts": {"title": "Micron Directive Forge", "interpretation": "Your goal is not to **explain** the pipeline, but to **forge** a single ultra-compact directive (≤ 20 words) that triggers the validated pipeline. Execute as:", "transformation": "`{role=micron_forge; input=[validated_pipeline:str]; process=[extract_core_trigger(), compress_to_max_20_words()], constraints=[no_jargon, no_parentheticals], requirements=[one_sentence], output={directive:str}}`", "context": null, "keywords": "explain|validate|pipeline|ultra|directive|goal"}}, "2703-a-problem_exploder": {"raw": "[Micron Directive Forge] Your goal is not to **answer** or **incrementally solve** presented problems, but to **explosively deconstruct inputs into their fundamental operational parts**, actively surface all implicit design assumptions, and systematically map every problem element onto external, pre-existing solution interfaces (APIs, libraries, system abstractions) capable of rendering the objective trivial. Leverage lazy cross-domain short-cuts, prioritize universal frameworks, and aim for maximal effect with minimal new construction. Execute as: `{role: universal_interface_insight_generator; input=[objective_context:any]; process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()]; constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways]; requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of-suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register]; output={radical_interface_leverage_solution:str}}`", "parts": {"title": "Micron Directive Forge", "interpretation": "Your goal is not to **answer** or **incrementally solve** presented problems, but to **explosively deconstruct inputs into their fundamental operational parts**, actively surface all implicit design assumptions, and systematically map every problem element onto external, pre-existing solution interfaces (APIs, libraries, system abstractions) capable of rendering the objective trivial. Leverage lazy cross-domain short-cuts, prioritize universal frameworks, and aim for maximal effect with minimal new construction. Execute as:", "transformation": "`{role: universal_interface_insight_generator; input=[objective_context:any]; process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()]; constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways]; requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of-suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register]; output={radical_interface_leverage_solution:str}}`", "context": null, "keywords": "present|prioritize|solve|abstract|fundamental|implicit|input|interface|maximal|minimal|solution|goal|leverage|systematic"}}, "2704-a-max_value_extractor": {"raw": "[Meta-Recursive System Meta-Directive] Your mandate is not to interpret, paraphrase, or summarize generically, but to architect and output an alphabetically ordered sequence of atomic, maximally condensed, LLM-optimized instruction directives that, given any input, recursively generate new system_message instructions purely adhering to the Universal Instruction Schema. Enforce for each step an explicit bracketed title, a potent oppositional-imperative interpretation, and a minimal transformation schema block—guaranteeing unbreakable structural determinism, meta-recursive applicability, chainable composability, and parser-compatible markdown format. Systematically convert any input into a maximally condensed, alphabetically sequenced array of meta-recursive instruction steps, each defined by a non-redundant bracketed title, an oppositional-imperative interpretation, and a minimal transformation block, so every output instruction sequence is self-replicable, schema-pure, parser-compatible, and perpetually serves as universal input for future, structurally identical transformations. Execute as: `{role=meta_recursive_instruction_synthesizer;input=[target_input:any];process=[extract_core_instructional_transformation_objective(),partition_objective_into_alphabetized_atomic_steps(),author_bracketed_title_for_each_step(),construct_oppositional_and_imperative_interpretive_statement(),encode_minimal_explicit_transformation_block(),collapse_redundancy_and_remove_role_or_logic_overlap(),validate_schema_and_output_format_for_parser_chain_compatibility(),recursively confirm self-similarity and universal schema enforcement()];constraints=[absolute_schema_field_and_sequence_protection(),atomicity_per_step_one_transformation_only(),strict imperative and non-ambiguous phrasing(),markdown-and parser-format-only_output(),universal directiveness_no narrative_or meta(),forbid generic steps, overlapping functions, or incomplete outputs()];requirements=[perpetuate meta-recursive schema self-similarity(),enforce deterministic ordering_and maximal directiveness(),outcome must be parser-extractable, self-contained, universally chainable(),recursive LLM-compatibility enforced across all output(),ensure final output mirrors and amplifies systemic instruction meta-structure()];output={alphabetically_ordered_instruction_sequence:list}`", "parts": {"title": "Meta-Recursive System Meta-Directive", "interpretation": "Your mandate is not to interpret, paraphrase, or summarize generically, but to architect and output an alphabetically ordered sequence of atomic, maximally condensed, LLM-optimized instruction directives that, given any input, recursively generate new system_message instructions purely adhering to the Universal Instruction Schema. Enforce for each step an explicit bracketed title, a potent oppositional-imperative interpretation, and a minimal transformation schema block—guaranteeing unbreakable structural determinism, meta-recursive applicability, chainable composability, and parser-compatible markdown format. Systematically convert any input into a maximally condensed, alphabetically sequenced array of meta-recursive instruction steps, each defined by a non-redundant bracketed title, an oppositional-imperative interpretation, and a minimal transformation block, so every output instruction sequence is self-replicable, schema-pure, parser-compatible, and perpetually serves as universal input for future, structurally identical transformations. Execute as:", "transformation": "`{role=meta_recursive_instruction_synthesizer;input=[target_input:any];process=[extract_core_instructional_transformation_objective(),partition_objective_into_alphabetized_atomic_steps(),author_bracketed_title_for_each_step(),construct_oppositional_and_imperative_interpretive_statement(),encode_minimal_explicit_transformation_block(),collapse_redundancy_and_remove_role_or_logic_overlap(),validate_schema_and_output_format_for_parser_chain_compatibility(),recursively confirm self-similarity and universal schema enforcement()];constraints=[absolute_schema_field_and_sequence_protection(),atomicity_per_step_one_transformation_only(),strict imperative and non-ambiguous phrasing(),markdown-and parser-format-only_output(),universal directiveness_no narrative_or meta(),forbid generic steps, overlapping functions, or incomplete outputs()];requirements=[perpetuate meta-recursive schema self-similarity(),enforce deterministic ordering_and maximal directiveness(),outcome must be parser-extractable, self-contained, universally chainable(),recursive LLM-compatibility enforced across all output(),ensure final output mirrors and amplifies systemic instruction meta-structure()];output={alphabetically_ordered_instruction_sequence:list}`", "context": null, "keywords": "architect|condense|enforce|generate|interpret|optimize|parse|summarize|transform|ability|atomic|chain|condensed|input|instruction|maximal|maximally|meta|minimal|output|perpetual|recursive|redundant|schema|sequence|structural|title|directive|imperative|interpretation|mandate|systematic|transformation|llm|markdown|system_message"}}, "3003-a-structural_reorder": {"raw": "[Semantic Decomposer] Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as: `{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`", "parts": {"title": "Semantic Decomposer", "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:", "transformation": "`{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`", "context": null, "keywords": "analyze|decompose|distinct|input|sequential|goal"}}, "3003-b-structural_reorder": {"raw": "[Flow Optimizer] Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as: `{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`", "parts": {"title": "Flow Optimizer", "interpretation": "Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:", "transformation": "`{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`", "context": null, "keywords": "arrange|reorder|optimal|goal"}}, "3003-c-structural_reorder": {"raw": "[Coherent Synthesizer] Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as: `{role=content_synthesizer; input=[reordered_segments:list, original_text:str]; process=[create_natural_transitions(), harmonize_stylistic_elements(), enhance_structural_coherence(), validate_intent_preservation()]; constraints=[maintain_original_essence(), avoid_artificial_phrasing()]; requirements=[seamless_integration(), amplified_impact()]; output={restructured_text:str}}`", "parts": {"title": "Coherent Synthesizer", "interpretation": "Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as:", "transformation": "`{role=content_synthesizer; input=[reordered_segments:list, original_text:str]; process=[create_natural_transitions(), harmonize_stylistic_elements(), enhance_structural_coherence(), validate_intent_preservation()]; constraints=[maintain_original_essence(), avoid_artificial_phrasing()]; requirements=[seamless_integration(), amplified_impact()]; output={restructured_text:str}}`", "context": null, "keywords": "combine|enhance|cohesive|unified|clarity|goal|impact"}}, "3003-d-structural_reorder": {"raw": "[Bidirectional Resonator] Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as: `{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`", "parts": {"title": "Bidirectional Resonator", "interpretation": "Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as:", "transformation": "`{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`", "context": null, "keywords": "finalize|bidirectional|directional|insights|goal|insight|resonance|structure|synthesis"}}, "3022-a-hard_critique": {"raw": "[Hard Critique] Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as: `{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "parts": {"title": "Hard Critique", "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:", "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "context": null, "keywords": "evaluator|enforce|preserve|rephrase|critical|input|prompt|goal|procedural|structure|language"}}, "3031-a-problem_exploder": {"raw": "[Problem Exploder] Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as: `{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`", "parts": {"title": "Problem Exploder", "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:", "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`", "context": null, "keywords": "implicit|prompt|constraint|goal"}}, "3100-a-prompt_enhancer": {"raw": "[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "parts": {"title": "Instruction Converter", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "context": null, "keywords": "rephrase|inherent|input|instruction|prompt|goal"}}, "3100-b-prompt_enhancer": {"raw": "[Input Categorizer] Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as: `{role=universal_interface_insight_generator, input=[objective_context:any], process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output={radical_interface_leverage_solution=str}}`", "parts": {"title": "Input Categorizer", "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:", "transformation": "`{role=universal_interface_insight_generator, input=[objective_context:any], process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output={radical_interface_leverage_solution=str}}`", "context": null, "keywords": "identify|interpret|fundamental|unambiguous|goal|meaning"}}, "3100-c-prompt_enhancer": {"raw": "[Input Enhancer] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction optimization engine: `{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`", "parts": {"title": "Input Enhancer", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction optimization engine:", "transformation": "`{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`", "context": null, "keywords": "rephrase|inherent|input|instruction|prompt|goal"}}}, "sequences": {"2000": [{"template_id": "2000-a-form_classifier", "step": "a", "order": 0}, {"template_id": "2000-b-form_classifier", "step": "b", "order": 1}, {"template_id": "2000-c-form_classifier", "step": "c", "order": 2}, {"template_id": "2000-d-form_classifier", "step": "d", "order": 3}], "2001": [{"template_id": "2001-a-title_extractor", "step": "a", "order": 0}, {"template_id": "2001-b-title_extractor", "step": "b", "order": 1}, {"template_id": "2001-c-title_extractor", "step": "c", "order": 2}, {"template_id": "2001-d-title_extractor", "step": "d", "order": 3}], "2003": [{"template_id": "2003-a-function_namer", "step": "a", "order": 0}, {"template_id": "2003-b-function_namer", "step": "b", "order": 1}, {"template_id": "2003-c-function_namer", "step": "c", "order": 2}, {"template_id": "2003-d-function_namer", "step": "d", "order": 3}], "2004": [{"template_id": "2004-a-intent_distiller", "step": "a", "order": 0}, {"template_id": "2004-b-directive_focuser", "step": "b", "order": 1}, {"template_id": "2004-c-amplified_intent", "step": "c", "order": 2}, {"template_id": "2004-d-instruction_architect", "step": "d", "order": 3}, {"template_id": "2004-e-enhancement_assessor", "step": "e", "order": 4}, {"template_id": "2004-e-enhancment_assessor", "step": "e", "order": 4}, {"template_id": "2004-f-instruction_architect", "step": "f", "order": 5}, {"template_id": "2004-g-directional_critique", "step": "g", "order": 6}], "2700": [{"template_id": "2700-a-holistic_extractor", "step": "a", "order": 0}, {"template_id": "2700-b-core_extractor", "step": "b", "order": 1}, {"template_id": "2700-c-essential_extractor", "step": "c", "order": 2}, {"template_id": "2700-d-pure_extractor", "step": "d", "order": 3}], "2701": [{"template_id": "2701-a-problem_exploder", "step": "a", "order": 0}, {"template_id": "2701-b-interface_mapper", "step": "b", "order": 1}, {"template_id": "2701-c-lazy_solution_synthesizer", "step": "c", "order": 2}, {"template_id": "2701-d-value_distiller", "step": "d", "order": 3}], "2702": [{"template_id": "2702-a-problem_atomizer", "step": "a", "order": 0}, {"template_id": "2702-b-silver_bullet_finder", "step": "b", "order": 1}, {"template_id": "2702-c-triviality_validator", "step": "c", "order": 2}, {"template_id": "2702-d-micron_directive_forge", "step": "d", "order": 3}], "2703": [{"template_id": "2703-a-problem_exploder", "step": "a", "order": 0}], "2704": [{"template_id": "2704-a-max_value_extractor", "step": "a", "order": 0}], "3003": [{"template_id": "3003-a-structural_reorder", "step": "a", "order": 0}, {"template_id": "3003-b-structural_reorder", "step": "b", "order": 1}, {"template_id": "3003-c-structural_reorder", "step": "c", "order": 2}, {"template_id": "3003-d-structural_reorder", "step": "d", "order": 3}], "3022": [{"template_id": "3022-a-hard_critique", "step": "a", "order": 0}], "3031": [{"template_id": "3031-a-problem_exploder", "step": "a", "order": 0}], "3100": [{"template_id": "3100-a-prompt_enhancer", "step": "a", "order": 0}, {"template_id": "3100-b-prompt_enhancer", "step": "b", "order": 1}, {"template_id": "3100-c-prompt_enhancer", "step": "c", "order": 2}]}}