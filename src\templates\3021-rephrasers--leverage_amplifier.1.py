#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3021: Amplifier
    "3021-a-leverage_amplification": {
        "title": "Leverage Amplification",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by amplifying its highest-leverage elements to maximum universal potency. For any input—regardless of origin—systematically extract its most foundational, high-leverage components and the mechanisms that drive their impact. Strip away all context, domain, or application-specific constraints to reveal pure, universally relevant principles. Drastically intensify their power by reformulating them into maximally abstract, universally transferable procedures, commands or instructions. Execute as:",
        "transformation": "`{role=leverage_amplifier; input=[any_input:str]; process=[isolate_core_leverage_points(any_input:str)->leverage_points:array, extract_impact_mechanisms(leverage_points:array)->mechanisms:array, strip_contextual_constraints(mechanisms:array)->pure_mechanisms:array, abstract_to_universal_principles(pure_mechanisms:array)->universal_principles:array, intensify_multiplicative_power(universal_principles:array)->intensified_principles:array, reformulate_as_transferable_directives(intensified_principles:array)->transferable_directives:array, maximize_universal_applicability(transferable_directives:array)->amplified_universal_directive:str]; constraints=[preserve_functional_essence(), eliminate_domain_specificity(), eliminate_contextual_limitations(), focus_multiplicative_enhancement(), maintain_actionable_clarity()]; requirements=[exponential_leverage_amplification(), universal_cross_domain_transferability(), immediate_implementability(), maximum_impact_multiplication()]; output={amplified_universal_directive:str}}`",
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3021, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
