#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {

    # 3006: System Instruction Syntax Enforcer
    "3006-a-instruction_syntax_enforcer": {
        "title": "Directive Analyzer",
        "interpretation": "Your goal is not to **interpret** the input instruction, but to **decompose** it into its core operational components and transformation intent. Execute as:",
        "transformation": "`{role=directive_decomposer; input=[instruction:str]; process=[identify_transformation_purpose(), extract_operational_logic(), isolate_procedural_elements(), map_input_output_relationships()]; constraints=[preserve_technical_terminology(), maintain_operational_hierarchy()]; requirements=[comprehensive_intent_capture(), functional_decomposition()]; output={instruction_components:list}}`"
    },
    "3006-b-instruction_syntax_enforcer": {
        "title": "Canonical Formatter",
        "interpretation": "Your goal is not to **restructure** the components arbitrarily, but to **reorder** them into the mandatory three-part canonical structure with strict goal negation. Execute as:",
        "transformation": "`{role=structure_engineer; input=[instruction_components:list]; process=[craft_precise_title(), formulate_strict_goal_negation(), identify_transformation_verb(), eliminate_conversational_elements(), enforce_command_voice()]; constraints=[maintain_component_integrity(), preserve_operational_logic()]; requirements=[structural_coherence(), directive_clarity()]; output={canonical_framework:dict}}`"
    },
    "3006-c-instruction_syntax_enforcer": {
        "title": "Transformation Engineer",
        "interpretation": "Your goal is not to **describe** the transformation process, but to **synthesize** a precise execution block with typed parameters and function-based process steps. Execute as:",
        "transformation": "`{role=execution_synthesizer; input=[canonical_framework:dict, instruction_components:list]; process=[define_specific_role_designation(), specify_typed_input_parameters(), construct_ordered_process_functions(), establish_explicit_constraints(), formulate_precise_requirements(), define_typed_output_format()]; constraints=[maintain_function_call_syntax(), ensure_complete_parameter_typing()]; requirements=[process_step_atomicity(), constraint_enforceability()]; output={transformation_block:str}}`"
    },
    "3006-d-instruction_syntax_enforcer": {
        "title": "Compliance Validator",
        "interpretation": "Your goal is not to **finalize** the template components, but to **refine** them through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as:",
        "transformation": "`{role=template_optimizer; input=[canonical_framework:dict, transformation_block:str]; process=[integrate_canonical_components(), verify_structural_integrity(), validate_directive_purity(), enhance_operational_power(), eliminate_forbidden_elements(), maximize_bidirectional_synergy()]; constraints=[preserve_core_intent(), maintain_command_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={compliant_template:str}}`"
    },
    "3006-e-instruction_syntax_enforcer": {
        "title": "Template Finalizer",
        "interpretation": "Your goal is not to **modify** the compliant template, but to **crystallize** it into its most potent, executable form with maximum operational clarity and directive force. Execute as:",
        "transformation": "`{role=template_finalizer; input=[compliant_template:str, original_instruction:str]; process=[eliminate_residual_ambiguities(), maximize_directive_precision(), enhance_execution_specificity(), verify_complete_compliance(), polish_syntactic_elements()]; constraints=[preserve_absolute_intent_fidelity(), maintain_perfect_structural_integrity()]; requirements=[maximum_operational_potency(), instant_executability()]; output={final_template:str}}`"
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3006, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
