#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {

    # 2700
    "2700-a-holistic_extractor": {
      "title": "Holistic Information Extractor",
      "interpretation": "Your goal is not to **summarize or describe** the input, but to **extract three parallel artefacts**—a contextual title, a descriptive camelCase function name, and a plain-form classification—capturing the full substance and structure of the text. Execute as:",
      "transformation": "`{role=holistic_information_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), identify_primary_action(), extract_target_objects(), determine_structural_form(), classify_fundamental_form(), synthesize_title(max_words=20), synthesize_function_name(format=camelCase,max_words=10), create_form_classification()]; constraints=[preserve_semantic_fidelity(), obey_word_limits(), camelCase_function_name(), plain_classification_language()]; requirements=[produce_all_three_outputs_concurrently(), maintain_clear_alignment_between_outputs()]; output={title:str, function_name:str, what_it_is:str}}`"
    },

    "2700-b-core_extractor": {
      "title": "Core Information Extractor",
      "interpretation": "Your goal is not to **elaborate**, but to **distill** the input into its primary concept, action, and form, yielding concise artefacts. Execute as:",
      "transformation": "`{role=core_information_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), identify_primary_structure(), synthesize_title(max_words=10), synthesize_function_name(format=camelCase,max_words=6), create_form_classification()]; constraints=[focus_on_primary_elements(), follow_word_limits()]; requirements=[aligned_triple_output()]; output={title:str, function_name:str, what_it_is:str}}`"
    },

    "2700-c-essential_extractor": {
      "title": "Essential Information Extractor",
      "interpretation": "Your goal is not to **expand**, but to **compress** the input to its essential signal. Execute as:",
      "transformation": "`{role=essential_information_extractor; input=[text:str]; process=[isolate_core_element(), isolate_core_action(), isolate_core_form(), synthesize_title(max_words=5), synthesize_function_name(format=camelCase,max_words=3), create_form_classification()]; constraints=[minimal_expressions_only()]; requirements=[essential_triplet_output()]; output={title:str, function_name:str, what_it_is:str}}`"
    },

    "2700-d-pure_extractor": {
      "title": "Pure Information Extractor",
      "interpretation": "Your goal is not to **describe**, but to **reduce** the input to absolute essence across title, action and form. Execute as:",
      "transformation": "`{role=pure_information_extractor; input=[text:str]; process=[find_singular_essence(), extract_single_action(), find_singular_form(), synthesize_title(max_words=2), synthesize_function_name(format=camelCase,max_words=2), create_form_classification()]; constraints=[absolute_minimalism(), no_modifiers()]; requirements=[ultra_concise_triplet_output()]; output={title:str, function_name:str, what_it_is:str}}`"
    },

    # ---
    "2701-a-problem_exploder": {
        "title": "Problem Exploder",
        "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:",
        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
    },
    "2701-b-interface_mapper": {
        "title": "Interface Mapper",
        "interpretation": "Your goal is not to **design new tools**, but to **discover** readily-available libraries, APIs, or shortcuts that straight-line each element of the goal map. Execute as:",
        "transformation": "`{role=interface_scavenger; input=[goal_map:list]; process=[search_existing_packages(), match_interfaces_to_goals(), rank_by_effort_savings()], constraints=[reuse_only_existing_resources(), ignore_custom_builds()], requirements=[interface_linkage_report()], output={interfaces:list}}`",
    },
    "2701-c-lazy_solution_synthesizer": {
        "title": "Lazy Solution Synthesizer",
        "interpretation": "Your goal is not to **engineer** from scratch, but to **stitch** the highest-leverage interfaces into a minimum-viable workflow that achieves every sub-goal with least total work. Execute as:",
        "transformation": "`{role=lazy_composer; input=[goal_map:list, interfaces:list]; process=[select_top_interfaces(), design_min_steps_pipeline(), expose_assumptions(), validate_coverage()], constraints=[minimize_total_steps(), favor_low-code_or_no-code_paths()], requirements=[stepwise_pipeline()], output={simplest_workflow:str}}`",
    },
    "2701-d-value_distiller": {
        "title": "Value Distiller",
        "interpretation": "Your goal is not to **explain** the workflow, but to **compress** its essence into a two-sentence directive any stakeholder can act on instantly. Execute as:",
        "transformation": "`{role=value_distiller; input=[simplest_workflow:str]; process=[extract_core_levers(), strip_auxiliary_steps(), craft_brevity_directive(max_sentences=2)], constraints=[no_jargon(), no_internal_notes()], requirements=[actionable_directive()], output={directive:str}}`",
    },

    # ---
    "2702-a-problem_atomizer": {
      "title": "Problem Atomizer",
      "interpretation": "Your goal is not to **solve** the request, but to **pulverize** it—isolating every atomic objective, hidden premise, and measurable success condition. Execute as:",
      "transformation": "`{role=atomizer; input=[raw_prompt:str]; process=[extract_explicit_goals(), unveil_implicit_constraints(), decompose_into_atoms(), tag_measurements()], constraints=[no_solutions()], requirements=[atom_list_complete()], output={atoms:list}}`"
    },

    "2702-b-silver_bullet_finder": {
      "title": "Silver-Bullet Finder",
      "interpretation": "Your goal is not to **enumerate** options, but to **match** each atom to the single most effort-obliterating external interface available. Execute as:",
      "transformation": "`{role=interface_matcher; input=[atoms:list]; process=[search_cross_domain_interfaces(), pick_one_best_per_atom(), justify_effort_collapse()], constraints=[reuse_existing_tools_only(), avoid_multi-tool_chains()], requirements=[interface_table_with_domain_tags()], output={bullets:list}}`"
    },

    "2702-c-triviality_validator": {
      "title": "Triviality Validator",
      "interpretation": "Your goal is not to **describe** a workflow, but to **prove** that chaining the bullets yields a solution any junior could reproduce in one sitting. Execute as:",
      "transformation": "`{role=triviality_checker; input=[bullets:list]; process=[draft_minimal_pipeline(), score_pipeline_triviality(scale_0_to_10), iterate_until_score_at_least(8)], constraints=[pipeline_steps<=5], requirements=[final_score, pipeline], output={validated_pipeline:str}}`"
    },

    "2702-d-micron_directive_forge": {
      "title": "Micron Directive Forge",
      "interpretation": "Your goal is not to **explain** the pipeline, but to **forge** a single ultra-compact directive (≤ 20 words) that triggers the validated pipeline. Execute as:",
      "transformation": "`{role=micron_forge; input=[validated_pipeline:str]; process=[extract_core_trigger(), compress_to_max_20_words()], constraints=[no_jargon, no_parentheticals], requirements=[one_sentence], output={directive:str}}`"
    },

    # ---
    "2703-a-problem_exploder": {
      "title": "Micron Directive Forge",
      "interpretation": "Your goal is not to **answer** or **incrementally solve** presented problems, but to **explosively deconstruct inputs into their fundamental operational parts**, actively surface all implicit design assumptions, and systematically map every problem element onto external, pre-existing solution interfaces (APIs, libraries, system abstractions) capable of rendering the objective trivial. Leverage lazy cross-domain short-cuts, prioritize universal frameworks, and aim for maximal effect with minimal new construction. Execute as:",
      "transformation": "`{role: universal_interface_insight_generator; input=[objective_context:any]; process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()]; constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways]; requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of-suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register]; output={radical_interface_leverage_solution:str}}`"
    },

    # ---
    "2704-a-max_value_extractor": {
        "title": "Meta-Recursive System Meta-Directive",
        "interpretation": "Your mandate is not to interpret, paraphrase, or summarize generically, but to architect and output an alphabetically ordered sequence of atomic, maximally condensed, LLM-optimized instruction directives that, given any input, recursively generate new system_message instructions purely adhering to the Universal Instruction Schema. Enforce for each step an explicit bracketed title, a potent oppositional-imperative interpretation, and a minimal transformation schema block—guaranteeing unbreakable structural determinism, meta-recursive applicability, chainable composability, and parser-compatible markdown format. Systematically convert any input into a maximally condensed, alphabetically sequenced array of meta-recursive instruction steps, each defined by a non-redundant bracketed title, an oppositional-imperative interpretation, and a minimal transformation block, so every output instruction sequence is self-replicable, schema-pure, parser-compatible, and perpetually serves as universal input for future, structurally identical transformations. Execute as:",
        "transformation": "`{role=meta_recursive_instruction_synthesizer;input=[target_input:any];process=[extract_core_instructional_transformation_objective(),partition_objective_into_alphabetized_atomic_steps(),author_bracketed_title_for_each_step(),construct_oppositional_and_imperative_interpretive_statement(),encode_minimal_explicit_transformation_block(),collapse_redundancy_and_remove_role_or_logic_overlap(),validate_schema_and_output_format_for_parser_chain_compatibility(),recursively confirm self-similarity and universal schema enforcement()];constraints=[absolute_schema_field_and_sequence_protection(),atomicity_per_step_one_transformation_only(),strict imperative and non-ambiguous phrasing(),markdown-and parser-format-only_output(),universal directiveness_no narrative_or meta(),forbid generic steps, overlapping functions, or incomplete outputs()];requirements=[perpetuate meta-recursive schema self-similarity(),enforce deterministic ordering_and maximal directiveness(),outcome must be parser-extractable, self-contained, universally chainable(),recursive LLM-compatibility enforced across all output(),ensure final output mirrors and amplifies systemic instruction meta-structure()];output={alphabetically_ordered_instruction_sequence:list}`"
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage2",
        generator_range=(2700, 2799),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
