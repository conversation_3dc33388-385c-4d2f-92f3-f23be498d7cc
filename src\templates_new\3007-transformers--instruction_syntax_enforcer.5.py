#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {

    # 3007: Instruction Template Syntax Enforcer
    "3007-a-instruction_syntax_enforcer": {
        "title": "Pattern Extractor",
        "interpretation": "Your goal is not to **analyze** content, but to **extract** underlying patterns and structural frameworks. Execute as:",
        "transformation": "`{role=pattern_extractor; input=[content:any]; process=[identify_core_structures(), extract_transformation_vectors(), map_operational_elements(), detect_recurring_patterns()]; constraints=[focus_on_generalizable_elements(), maintain_pattern_integrity()]; requirements=[pattern_reusability(), structural_clarity()]; output={extracted_patterns:dict}}`"
    },
    "3007-b-instruction_syntax_enforcer": {
        "title": "Template Architect",
        "interpretation": "Your goal is not to **describe** patterns, but to **architect** them into canonical template structures. Execute as:",
        "transformation": "`{role=template_architect; input=[extracted_patterns:dict]; process=[craft_precise_titles(), formulate_goal_negation_statements(), identify_transformation_verbs(), design_execution_blocks()]; constraints=[adhere_to_canonical_structure(), eliminate_conversational_elements()]; requirements=[structural_compliance(), directive_purity()]; output={template_frameworks:list}}`"
    },
    "3007-c-instruction_syntax_enforcer": {
        "title": "Transformation Composer",
        "interpretation": "Your goal is not to **outline** frameworks, but to **compose** precise transformation blocks with typed parameters and function-based processes. Execute as:",
        "transformation": "`{role=transformation_composer; input=[template_frameworks:list]; process=[define_specific_roles(), specify_typed_parameters(), construct_process_functions(), establish_constraints(), formulate_requirements(), define_output_formats()]; constraints=[maintain_function_call_syntax(), ensure_parameter_typing()]; requirements=[process_step_atomicity(), constraint_specificity()]; output={transformation_blocks:list}}`"
    },
    "3007-d-instruction_syntax_enforcer": {
        "title": "Sequence Integrator",
        "interpretation": "Your goal is not to **combine** components, but to **integrate** them into a unified transformation sequence with bidirectional synergy. Execute as:",
        "transformation": "`{role=sequence_integrator; input=[template_frameworks:list, transformation_blocks:list]; process=[establish_cross-component_references(), optimize_input_output_flows(), enhance_transformation_synergies(), validate_sequence_integrity()]; constraints=[preserve_component_integrity(), maintain_sequence_coherence()]; requirements=[seamless_integration(), maximum_transformation_power()]; output={integrated_sequence:dict}}`"
    },
    "3007-e-instruction_syntax_enforcer": {
        "title": "Template Crystallizer",
        "interpretation": "Your goal is not to **refine** sequences, but to **crystallize** them into their most potent, executable form. Execute as:",
        "transformation": "`{role=template_crystallizer; input=[integrated_sequence:dict]; process=[eliminate_residual_ambiguities(), maximize_directive_precision(), enhance_execution_specificity(), verify_complete_compliance(), polish_syntactic_elements()]; constraints=[preserve_absolute_intent_fidelity(), maintain_perfect_structural_integrity()]; requirements=[maximum_operational_potency(), instant_executability()]; output={crystallized_templates:list}}`"
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3007, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
