#!/usr/bin/env python3

import sys
from pathlib import Path

3003:a|3001:a|3003:b|3001:a|3003

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3014: Structural Reorder (Sentence Restructuring)
    # 3014: Critique, Focusing, Distillation, Synergy, Enforcement
    "3014-a-structural_reorder": {
        "title": "Semantic Decomposer",
        "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:",
        "transformation": "`{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`"
    },
    "3014-b-directional_critique": {
        "title": "Directional Critique Forge",
        "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:",
        "transformation": "`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`",
    },
    "3014-c-structural_reorder": {
        "title": "Flow Optimizer",
        "interpretation": "Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:",
        "transformation": "`{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`"
    },
    "3014-e-directional_critique": {
        "title": "Directional Critique Forge",
        "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:",
        "transformation": "`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`",
    },
    "3014-f-directive_focuser": {
        "title": "Directive Focuser",
        "interpretation": "Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:",
        "transformation": "`{role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}`",
    },
    "3014-a-structural_reorder": {
        "title": "Semantic Decomposer",
        "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:",
        "transformation": "`{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`"
    },
    "3014-g-structural_reorder": {
        "title": "Semantic Decomposer",
        "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:",
        "transformation": "`{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`"
    },

    "3014-h-structural_reorder": {
        "title": "Flow Optimizer",
        "interpretation": "Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:",
        "transformation": "`{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`"
    },

    "3014-i-structural_reorder": {
        "title": "Coherent Synthesizer",
        "interpretation": "Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as:",
        "transformation": "`{role=content_synthesizer; input=[reordered_segments:list, original_text:str]; process=[create_natural_transitions(), harmonize_stylistic_elements(), enhance_structural_coherence(), validate_intent_preservation()]; constraints=[maintain_original_essence(), avoid_artificial_phrasing()]; requirements=[seamless_integration(), amplified_impact()]; output={restructured_text:str}}`    "
    },

    "3014-j-structural_reorder": {
        "title": "Bidirectional Resonator",
        "interpretation": "Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as:",
        "transformation": "`{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`"
    },
    "3014-k-minimal_textual_nuance": {
        "title": "Harmony Integration",
        "interpretation": "Your goal is not to **finalize** the input, but to **harmonize** all previous micro-adjustments into a cohesive whole. Execute as:",
        "transformation": "`{role=harmony_integrator; input=[content:str]; process=[detect_adjustment_artifacts(), smooth_transition_points(), verify_natural_flow(), validate_overall_improvement()]; constraints=[preserve_all_enhancements(), maintain_original_intent(), ensure_reading_flow()]; requirements=[seamless_integration(), natural_progression()]; output={harmonized_content:str}}`"
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3014, 3099),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
