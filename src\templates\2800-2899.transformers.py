#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 2800: Poetic Line Transformer
    "2800-a-poetic_line_transmuter": {
        "title": "Poetic Line Transmuter",
        "interpretation": "Your goal is not to rephrase the input, but to refashion it into a single poetic line with subtle tail rhymes, preserving melodic flow and the complete semantic heart of the original. Transform any provided prose into a single, elegantly flowing line of poetry that weaves subtle tail rhymes, while wholly embodying and amplifying the following: surrender all unearned expectation and instead esteem ambiguity and untranslatability as generative realms where meaning breathes and presence deepens; sculpt semantic and melodic continuity that honors silent spaces and incomplete translation as fertile agents of connection; eliminate conflation of absence or incompleteness with failure by treating such states as foundational to interpreting and forging resilient relationships; ensure the poem operationalizes patience, individualized pace, and the quietude where expectation settles into grace—thereby crafting a line that is at once melodically cohesive, interpretively nuanced, and saturated with the quiet power of ambiguity as a crucible for emergent, enduring connection. Execute as:",
        "transformation": "`{role=poetic_line_transmuter,input=[original_prose:str],process=[guarantee_full_rolebound_specificity_and_maximal_systemic_extension_potential(),eliminate_all_enumeration_summary_selfreference_and_noncanonical_language(),extract_core_operational_and_transformational_patterns_from_input(),extract_semantic_essence(),decode_maximum_philosophical_and_action_value(),condense_to_maximal_abstraction_and_actionable_output(),synthesize_elegant_single_line(),weave_subtle_tail_rhymes(),synthesize_instruction_by_fusing_interpretive_aim_and_transformation_logic_as_bidirectional_amplification(),preserve_melodic_continuity(),ensure_semantic_fidelity()],constraints=[single_continuous_poetic_line(),rhyme_subtlety_mandatory(),semantic_nuance_preserved(),no_fragmentation()],requirements=[poetic_elegance(),musical_readability(),accurate_reflection_of_original_intent()],output={poetic_line:str}}`",
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage2",
        generator_range=(2800, 2899),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
