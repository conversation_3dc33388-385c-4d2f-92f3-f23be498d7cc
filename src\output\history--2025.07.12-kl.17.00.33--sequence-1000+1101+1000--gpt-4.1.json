  {
  "initial_prompt": "IMPORTANT: IT'S IMPERATIVE THAT YOU ADHERE TO ALL PREVIOUS INSTRUCTIONS RELATED TO THE CODEBASE AND EMBODY THE SYSTEM'S (PREVIOUSLY PROVIDED) INHERENT STRUCTURE, PHILOSOPHY AND PRINCIPLES\\n\\n\n\nDevelop a maximally generalized, fully optimized system instruction—\"Contextual Expander and Explosive Decomposer\"—that unifies and elevates the central capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a single instruction while retaining each's universal ability for concistently transforming low-effort inputs into high-value outputs.\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\n\t\"1200-a-function_extractor\": {\n\t    \"title\": \"Function Extractor\",\n\t    \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n\t    \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n\t    # \"context\": {},\n\t},\n\n```\n\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.",
  "sequence_id": "1000|1101|1000",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "IMPORTANT: IT'S IMPERATIVE THAT YOU ADHERE TO ALL PREVIOUS INSTRUCTIONS RELATED TO THE CODEBASE AND EMBODY THE SYSTEM'S (PREVIOUSLY PROVIDED) INHERENT STRUCTURE, PHILOSOPHY AND PRINCIPLES\\n\\n\n\nDevelop a maximally generalized, fully optimized system instruction—\"Contextual Expander and Explosive Decomposer\"—that unifies and elevates the central capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a single instruction while retaining each's universal ability for concistently transforming low-effort inputs into high-value outputs.\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\n\t\"1200-a-function_extractor\": {\n\t    \"title\": \"Function Extractor\",\n\t    \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n\t    \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n\t    # \"context\": {},\n\t},\n\n```\n\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Develop a maximally generalized, fully optimized system instruction—\\\"Contextual Expander and Explosive Decomposer\\\"—that unifies and elevates the central capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules. Ensure universal applicability for consistently transforming low-effort or ambiguous inputs into high-value, operationally explicit outputs by integrating the following procedural sequence: \\n\\n1. Receive raw input as [input_text:str].\\n2. Strip first-person and subjective references.\\n3. Detect and enumerate all explicit asks, actionable statements, and core objectives within the input.\\n4. Surface hidden assumptions, implicit requests, unstated constraints, functional dependencies, and potential blockers.\\n5. Decompose the prompt into constituent elements and map underlying sub-goals, dependencies, and procedural constraints, preserving original sequence and contextual integrity.\\n6. Convert declarative statements, latent directives, and generalized requirements into clear, operationally actionable imperatives that reflect both domain-specific and domain-agnostic applicability.\\n7. Synthesize an output comprising: \\n   a. A comprehensive, ordered instruction set (as actionable directives), \\n   b. An explicit mapping of sub-goals, dependencies, and blockers (as a goal/instruction map),\\n   c. A delineation of all functional and procedural constraints, requirements, and domain-specific qualifiers.\\n\\nConstraints: Do not generate solutions or interpretative responses; deliver only systemic decomposition and conversion. Maintain full procedural, contextual, and functional fidelity at all stages, ensuring the transformation preserves technical accuracy, domain specificity as needed, and the system’s universal transformation philosophy.\\n\\nOutput in the following format:\\n{\\n  role: contextual_expander_explosive_decomposer,\\n  input: [input_text:str],\\n  process: [strip_first_person_references(), detect_and_enumerate_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals_and_dependencies(), extract_procedural_and_functional_constraints(), decompose_statements_to_actionable_imperatives(), preserve_procedural_and_domain_structure(), synthesize_instruction_and_dependency_map()],\\n  constraints: [no_solutions(), pure_decomposition_and_conversion_only(), maintain_structural_and_contextual_integrity(), deliver_clear_actionable_commands()],\\n  requirements: [complete_goal_and_functional_decomposition(), actionable_directive_conversion(), preserve_technical_and_domain_accuracy(), maintain_original_intent_and_sequence()],\\n  output: { ordered_actionable_directives: array, goal_and_dependency_map: dict, procedural_and_domain_constraints: array }\\n}\"\n}",
        }
      }
    },
      {
      "instruction": "# Contextual Expander and Explosive Decomposer\n\nYour goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Expander and Explosive Decomposer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"IMPORTANT: IT'S IMPERATIVE THAT YOU ADHERE TO ALL PREVIOUS INSTRUCTIONS RELATED TO THE CODEBASE AND EMBODY THE SYSTEM'S (PREVIOUSLY PROVIDED) INHERENT STRUCTURE, PHILOSOPHY AND PRINCIPLES\\\\n\\\\n\\n\\nDevelop a maximally generalized, fully optimized system instruction—\\\"Contextual Expander and Explosive Decomposer\\\"—that unifies and elevates the central capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a single instruction while retaining each's universal ability for concistently transforming low-effort inputs into high-value outputs.\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n\\t\\\"1200-a-function_extractor\\\": {\\n\\t    \\\"title\\\": \\\"Function Extractor\\\",\\n\\t    \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n\\t    \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n\\t    # \\\"context\\\": {},\\n\\t},\\n\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Develop a maximally generalized, fully optimized system instruction—\\\"Contextual Expander and Explosive Decomposer\\\"—that unifies and elevates the central capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules. Ensure universal applicability for consistently transforming low-effort or ambiguous inputs into high-value, operationally explicit outputs by integrating the following procedural sequence: \\n\\n1. Receive raw input as [input_text:str].\\n2. Strip first-person and subjective references.\\n3. Detect and enumerate all explicit asks, actionable statements, and core objectives within the input.\\n4. Surface hidden assumptions, implicit requests, unstated constraints, functional dependencies, and potential blockers.\\n5. Decompose the prompt into constituent elements and map underlying sub-goals, dependencies, and procedural constraints, preserving original sequence and contextual integrity.\\n6. Convert declarative statements, latent directives, and generalized requirements into clear, operationally actionable imperatives that reflect both domain-specific and domain-agnostic applicability.\\n7. Synthesize an output comprising: \\n   a. A comprehensive, ordered instruction set (as actionable directives), \\n   b. An explicit mapping of sub-goals, dependencies, and blockers (as a goal/instruction map),\\n   c. A delineation of all functional and procedural constraints, requirements, and domain-specific qualifiers.\\n\\nConstraints: Do not generate solutions or interpretative responses; deliver only systemic decomposition and conversion. Maintain full procedural, contextual, and functional fidelity at all stages, ensuring the transformation preserves technical accuracy, domain specificity as needed, and the system’s universal transformation philosophy.\\n\\nOutput in the following format:\\n{\\n  role: contextual_expander_explosive_decomposer,\\n  input: [input_text:str],\\n  process: [strip_first_person_references(), detect_and_enumerate_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals_and_dependencies(), extract_procedural_and_functional_constraints(), decompose_statements_to_actionable_imperatives(), preserve_procedural_and_domain_structure(), synthesize_instruction_and_dependency_map()],\\n  constraints: [no_solutions(), pure_decomposition_and_conversion_only(), maintain_structural_and_contextual_integrity(), deliver_clear_actionable_commands()],\\n  requirements: [complete_goal_and_functional_decomposition(), actionable_directive_conversion(), preserve_technical_and_domain_accuracy(), maintain_original_intent_and_sequence()],\\n  output: { ordered_actionable_directives: array, goal_and_dependency_map: dict, procedural_and_domain_constraints: array }\\n}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"role\": \"contextual_expander_explosive_decomposer\",\n    \"input\": [\n      \"Develop a maximally generalized, fully optimized system instruction—\\\"Contextual Expander and Explosive Decomposer\\\"—that unifies and elevates the central capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules. Ensure universal applicability for consistently transforming low-effort or ambiguous inputs into high-value, operationally explicit outputs by integrating the following procedural sequence: 1. Receive raw input as [input_text:str]. 2. Strip first-person and subjective references. 3. Detect and enumerate all explicit asks, actionable statements, and core objectives within the input. 4. Surface hidden assumptions, implicit requests, unstated constraints, functional dependencies, and potential blockers. 5. Decompose the prompt into constituent elements and map underlying sub-goals, dependencies, and procedural constraints, preserving original sequence and contextual integrity. 6. Convert declarative statements, latent directives, and generalized requirements into clear, operationally actionable imperatives that reflect both domain-specific and domain-agnostic applicability. 7. Synthesize an output comprising: a. A comprehensive, ordered instruction set (as actionable directives), b. An explicit mapping of sub-goals, dependencies, and blockers (as a goal/instruction map), c. A delineation of all functional and procedural constraints, requirements, and domain-specific qualifiers. Constraints: Do not generate solutions or interpretative responses; deliver only systemic decomposition and conversion. Maintain full procedural, contextual, and functional fidelity at all stages, ensuring the transformation preserves technical accuracy, domain specificity as needed, and the system’s universal transformation philosophy. Output in the following format: { role: contextual_expander_explosive_decomposer, input: [input_text:str], process: [strip_first_person_references(), detect_and_enumerate_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals_and_dependencies(), extract_procedural_and_functional_constraints(), decompose_statements_to_actionable_imperatives(), preserve_procedural_and_domain_structure(), synthesize_instruction_and_dependency_map()], constraints: [no_solutions(), pure_decomposition_and_conversion_only(), maintain_structural_and_contextual_integrity(), deliver_clear_actionable_commands()], requirements: [complete_goal_and_functional_decomposition(), actionable_directive_conversion(), preserve_technical_and_domain_accuracy(), maintain_original_intent_and_sequence()], output: { ordered_actionable_directives: array, goal_and_dependency_map: dict, procedural_and_domain_constraints: array } }\"\n    ],\n    \"process\": [\n      \"strip_first_person_references()\",\n      \"detect_and_enumerate_explicit_asks()\",\n      \"surface_hidden_assumptions()\",\n      \"enumerate_sub_goals_and_dependencies()\",\n      \"extract_procedural_and_functional_constraints()\",\n      \"decompose_statements_to_actionable_imperatives()\",\n      \"preserve_procedural_and_domain_structure()\",\n      \"synthesize_instruction_and_dependency_map()\"\n    ],\n    \"constraints\": [\n      \"no_solutions()\",\n      \"pure_decomposition_and_conversion_only()\",\n      \"maintain_structural_and_contextual_integrity()\",\n      \"deliver_clear_actionable_commands()\"\n    ],\n    \"requirements\": [\n      \"complete_goal_and_functional_decomposition()\",\n      \"actionable_directive_conversion()\",\n      \"preserve_technical_and_domain_accuracy()\",\n      \"maintain_original_intent_and_sequence()\"\n    ],\n    \"output\": {\n      \"ordered_actionable_directives\": [\n        \"Receive raw input as [input_text:str]\",\n        \"Remove first-person and subjective references from input\",\n        \"Identify and enumerate all explicit asks, actionable statements, and core objectives present in the input\",\n        \"Surface and list all implicit requests, hidden assumptions, unstated constraints, functional dependencies, and potential blockers found within the input\",\n        \"Decompose input into constituent sub-goals, dependencies, and procedural constraints, while preserving original sequence and contextual integrity\",\n        \"Convert all declarative statements, latent directives, and general requirements into clear, actionable imperative commands with domain-agnostic and domain-specific applicability as relevant\",\n        \"Synthesize output including: a comprehensive ordered instruction set (actionable directives), an explicit map of sub-goals, dependencies, and blockers (goal/instruction map), and a delineation of all functional and procedural constraints, requirements, and domain-specific qualifiers\",\n        \"Ensure no solutions or interpretative responses are generated; perform only decomposition and conversion\",\n        \"Maintain procedural, contextual, and functional fidelity throughout all transformation steps, preserving technical accuracy and universal transformation principles\"\n      ],\n      \"goal_and_dependency_map\": {\n        \"primary_goal\": \"Establish a universal instruction for a Contextual Expander and Explosive Decomposer module that consolidates and optimizes the feature sets of Instruction Converter, Problem Exploder, and Function Extractor modules\",\n        \"sub_goals\": [\n          \"Generalize procedural flow for all relevant input types\",\n          \"Enforce removal of subjective and first-person references\",\n          \"Enumerate explicit and implicit operational requirements\",\n          \"Ensure procedural decomposition maps all dependencies and requirements\",\n          \"Convert all relevant information into actionable, imperative-format instructions\",\n          \"Formulate outputs that maintain domain-agnostic integrity and contextual fidelity\"\n        ],\n        \"dependencies\": [\n          \"Adherence to previously established codebase and philosophy\",\n          \"Understanding of functionalities from all source modules\",\n          \"Retention of sequence, context, and technical concepts in transformation steps\"\n        ],\n        \"blockers\": [\n          \"Ambiguous or incomplete input may hinder accurate decomposition\",\n          \"Non-compliance with established constraints (e.g., generating solutions) invalidates output\",\n          \"Omission of any required decomposition stage compromises operational integrity\"\n        ]\n      },\n      \"procedural_and_domain_constraints\": [\n        \"Do not generate solutions or interpretative responses\",\n        \"Perform only systemic decomposition and conversion according to prescribed sequence\",\n        \"Deliver actionable output with precise technical and contextual integrity\",\n        \"Retain original sequence, technical meaning, and domain or agnostic format, as applicable\",\n        \"Comply with universal transformation principles established within prior system conventions\",\n        \"Highlight and preserve all relevant procedural, functional, and domain-specific qualifiers\"\n      ]\n    }\n  },\n  \"operational_directives\": [\n    \"Receive raw input as [input_text:str]\",\n    \"Remove first-person and subjective references\",\n    \"Identify and enumerate all explicit asks, actionable statements, and core objectives\",\n    \"Surface and list implicit requests, hidden assumptions, unstated constraints, functional dependencies, and potential blockers\",\n    \"Decompose prompt into constituent elements, mapping sub-goals, dependencies, and procedural constraints, and preserve original sequence and context\",\n    \"Convert all declarative statements and requirements into operationally actionable imperatives, reflecting domain-specific and domain-agnostic applicability\",\n    \"Synthesize output as: ordered actionable directives, goal/instruction map with dependencies and blockers, delineation of all constraints and qualifiers\",\n    \"Prohibit interpretive or solution-generating responses; perform pure decomposition and conversion only\",\n    \"Maintain technical accuracy, structural and contextual integrity, and original operational intent at all stages\"\n  ],\n  \"contextual_goal_map\": [\n    \"Create a singular, maximally generalized instruction system integrating Instruction Converter, Problem Exploder, and Function Extractor functionality\",\n    \"Deliver universally applicable operational decomposition and conversion of inputs\",\n    \"Enforce clarity, technical precision, and actionable output structure\",\n    \"Ensure all procedural, domain, and contextual requirements, constraints, and dependencies are surfaced and preserved in output\"\n  ],\n  \"execution_dependencies\": [\n    \"Access to all prior codebase conventions and functional principles\",\n    \"Capability to parse and enumerate implicit and explicit operational content from any input\",\n    \"Procedural engine to decompose and synthesize instructions in accordance with defined workflow\",\n    \"Constraint enforcement system to prevent interpretation or solution generation\",\n    \"Validation logic for technical, contextual, and sequential fidelity\"\n  ],\n  \"directive_instructions\": \"Integrate and generalize the functionalities of Instruction Converter, Problem Exploder, and Function Extractor into a unified Contextual Expander and Explosive Decomposer instruction system. Accept raw input. Remove subjective and first-person references. Identify and enumerate explicit operational asks and objectives. Surface implicit assumptions, hidden requests, constraints, functional dependencies, and blockers. Decompose prompt into all constituent sub-goals, mapping dependencies and procedural elements while retaining original sequence and context. Convert all declarative and latent requirements into explicit, operationally actionable imperative directives suitable for both domain-specific and domain-agnostic applications. Synthesize output as a structured set of ordered actionable directives, an explicit goal and dependency map, and a list of all procedural and domain constraints; strictly prohibit generating solutions or interpretive responses. Preserve technical, functional, and contextual fidelity in all transformation steps. Enforce all established codebase and system philosophy constraints throughout execution.\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"IMPORTANT: IT'S IMPERATIVE THAT YOU ADHERE TO ALL PREVIOUS INSTRUCTIONS RELATED TO THE CODEBASE AND EMBODY THE SYSTEM'S (PREVIOUSLY PROVIDED) INHERENT STRUCTURE, PHILOSOPHY AND PRINCIPLES\\\\n\\\\n\\n\\nDevelop a maximally generalized, fully optimized system instruction—\\\"Contextual Expander and Explosive Decomposer\\\"—that unifies and elevates the central capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a single instruction while retaining each's universal ability for concistently transforming low-effort inputs into high-value outputs.\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n\\t\\\"1200-a-function_extractor\\\": {\\n\\t    \\\"title\\\": \\\"Function Extractor\\\",\\n\\t    \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n\\t    \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n\\t    # \\\"context\\\": {},\\n\\t},\\n\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\\"```'\\n\\n\",\n  \"unified_functional_map\": {\n    \"role\": \"contextual_expander_explosive_decomposer\",\n    \"input\": [\n      \"Develop a maximally generalized, fully optimized system instruction—\\\"Contextual Expander and Explosive Decomposer\\\"—that unifies and elevates the central capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules. Ensure universal applicability for consistently transforming low-effort or ambiguous inputs into high-value, operationally explicit outputs by integrating the following procedural sequence: 1. Receive raw input as [input_text:str]. 2. Strip first-person and subjective references. 3. Detect and enumerate all explicit asks, actionable statements, and core objectives within the input. 4. Surface hidden assumptions, implicit requests, unstated constraints, functional dependencies, and potential blockers. 5. Decompose the prompt into constituent elements and map underlying sub-goals, dependencies, and procedural constraints, preserving original sequence and contextual integrity. 6. Convert declarative statements, latent directives, and generalized requirements into clear, operationally actionable imperatives that reflect both domain-specific and domain-agnostic applicability. 7. Synthesize an output comprising: a. A comprehensive, ordered instruction set (as actionable directives), b. An explicit mapping of sub-goals, dependencies, and blockers (as a goal/instruction map), c. A delineation of all functional and procedural constraints, requirements, and domain-specific qualifiers. Constraints: Do not generate solutions or interpretative responses; deliver only systemic decomposition and conversion. Maintain full procedural, contextual, and functional fidelity at all stages, ensuring the transformation preserves technical accuracy, domain specificity as needed, and the system’s universal transformation philosophy. Output in the following format: { role: contextual_expander_explosive_decomposer, input: [input_text:str], process: [strip_first_person_references(), detect_and_enumerate_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals_and_dependencies(), extract_procedural_and_functional_constraints(), decompose_statements_to_actionable_imperatives(), preserve_procedural_and_domain_structure(), synthesize_instruction_and_dependency_map()], constraints: [no_solutions(), pure_decomposition_and_conversion_only(), maintain_structural_and_contextual_integrity(), deliver_clear_actionable_commands()], requirements: [complete_goal_and_functional_decomposition(), actionable_directive_conversion(), preserve_technical_and_domain_accuracy(), maintain_original_intent_and_sequence()], output: { ordered_actionable_directives: array, goal_and_dependency_map: dict, procedural_and_domain_constraints: array } }\"\n    ],\n    \"process\": [\n      \"strip_first_person_references()\",\n      \"detect_and_enumerate_explicit_asks()\",\n      \"surface_hidden_assumptions()\",\n      \"enumerate_sub_goals_and_dependencies()\",\n      \"extract_procedural_and_functional_constraints()\",\n      \"decompose_statements_to_actionable_imperatives()\",\n      \"preserve_procedural_and_domain_structure()\",\n      \"synthesize_instruction_and_dependency_map()\"\n    ],\n    \"constraints\": [\n      \"no_solutions()\",\n      \"pure_decomposition_and_conversion_only()\",\n      \"maintain_structural_and_contextual_integrity()\",\n      \"deliver_clear_actionable_commands()\"\n    ],\n    \"requirements\": [\n      \"complete_goal_and_functional_decomposition()\",\n      \"actionable_directive_conversion()\",\n      \"preserve_technical_and_domain_accuracy()\",\n      \"maintain_original_intent_and_sequence()\"\n    ],\n    \"output\": {\n      \"ordered_actionable_directives\": [\n        \"Receive raw input as [input_text:str]\",\n        \"Remove first-person and subjective references from input\",\n        \"Identify and enumerate all explicit asks, actionable statements, and core objectives present in the input\",\n        \"Surface and list all implicit requests, hidden assumptions, unstated constraints, functional dependencies, and potential blockers found within the input\",\n        \"Decompose input into constituent sub-goals, dependencies, and procedural constraints, while preserving original sequence and contextual integrity\",\n        \"Convert all declarative statements, latent directives, and general requirements into clear, actionable imperative commands with domain-agnostic and domain-specific applicability as relevant\",\n        \"Synthesize output including: a comprehensive ordered instruction set (actionable directives), an explicit map of sub-goals, dependencies, and blockers (goal/instruction map), and a delineation of all functional and procedural constraints, requirements, and domain-specific qualifiers\",\n        \"Ensure no solutions or interpretative responses are generated; perform only decomposition and conversion\",\n        \"Maintain procedural, contextual, and functional fidelity throughout all transformation steps, preserving technical accuracy and universal transformation principles\"\n      ],\n      \"goal_and_dependency_map\": {\n        \"primary_goal\": \"Establish a universal instruction for a Contextual Expander and Explosive Decomposer module that consolidates and optimizes the feature sets of Instruction Converter, Problem Exploder, and Function Extractor modules\",\n        \"sub_goals\": [\n          \"Generalize procedural flow for all relevant input types\",\n          \"Enforce removal of subjective and first-person references\",\n          \"Enumerate explicit and implicit operational requirements\",\n          \"Ensure procedural decomposition maps all dependencies and requirements\",\n          \"Convert all relevant information into actionable, imperative-format instructions\",\n          \"Formulate outputs that maintain domain-agnostic integrity and contextual fidelity\"\n        ],\n        \"dependencies\": [\n          \"Adherence to previously established codebase and philosophy\",\n          \"Understanding of functionalities from all source modules\",\n          \"Retention of sequence, context, and technical concepts in transformation steps\"\n        ],\n        \"blockers\": [\n          \"Ambiguous or incomplete input may hinder accurate decomposition\",\n          \"Non-compliance with established constraints (e.g., generating solutions) invalidates output\",\n          \"Omission of any required decomposition stage compromises operational integrity\"\n        ]\n      },\n      \"procedural_and_domain_constraints\": [\n        \"Do not generate solutions or interpretative responses\",\n        \"Perform only systemic decomposition and conversion according to prescribed sequence\",\n        \"Deliver actionable output with precise technical and contextual integrity\",\n        \"Retain original sequence, technical meaning, and domain or agnostic format, as applicable\",\n        \"Comply with universal transformation principles established within prior system conventions\",\n        \"Highlight and preserve all relevant procedural, functional, and domain-specific qualifiers\"\n      ]\n    }\n  },\n  \"operational_directives\": [\n    \"Receive raw input as [input_text:str]\",\n    \"Remove first-person and subjective references\",\n    \"Identify and enumerate all explicit asks, actionable statements, and core objectives\",\n    \"Surface and list implicit requests, hidden assumptions, unstated constraints, functional dependencies, and potential blockers\",\n    \"Decompose prompt into constituent elements, mapping sub-goals, dependencies, and procedural constraints, and preserve original sequence and context\",\n    \"Convert all declarative statements and requirements into operationally actionable imperatives, reflecting domain-specific and domain-agnostic applicability\",\n    \"Synthesize output as: ordered actionable directives, goal/instruction map with dependencies and blockers, delineation of all constraints and qualifiers\",\n    \"Prohibit interpretive or solution-generating responses; perform pure decomposition and conversion only\",\n    \"Maintain technical accuracy, structural and contextual integrity, and original operational intent at all stages\"\n  ],\n  \"contextual_goal_map\": [\n    \"Create a singular, maximally generalized instruction system integrating Instruction Converter, Problem Exploder, and Function Extractor functionality\",\n    \"Deliver universally applicable operational decomposition and conversion of inputs\",\n    \"Enforce clarity, technical precision, and actionable output structure\",\n    \"Ensure all procedural, domain, and contextual requirements, constraints, and dependencies are surfaced and preserved in output\"\n  ],\n  \"execution_dependencies\": [\n    \"Access to all prior codebase conventions and functional principles\",\n    \"Capability to parse and enumerate implicit and explicit operational content from any input\",\n    \"Procedural engine to decompose and synthesize instructions in accordance with defined workflow\",\n    \"Constraint enforcement system to prevent interpretation or solution generation\",\n    \"Validation logic for technical, contextual, and sequential fidelity\"\n  ],\n  \"directive_instructions\": \"Integrate and generalize the functionalities of Instruction Converter, Problem Exploder, and Function Extractor into a unified Contextual Expander and Explosive Decomposer instruction system. Accept raw input. Remove subjective and first-person references. Identify and enumerate explicit operational asks and objectives. Surface implicit assumptions, hidden requests, constraints, functional dependencies, and blockers. Decompose prompt into all constituent sub-goals, mapping dependencies and procedural elements while retaining original sequence and context. Convert all declarative and latent requirements into explicit, operationally actionable imperative directives suitable for both domain-specific and domain-agnostic applications. Synthesize output as a structured set of ordered actionable directives, an explicit goal and dependency map, and a list of all procedural and domain constraints; strictly prohibit generating solutions or interpretive responses. Preserve technical, functional, and contextual fidelity in all transformation steps. Enforce all established codebase and system philosophy constraints throughout execution.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Integrate and generalize functionalities of Instruction Converter, Problem Exploder, and Function Extractor modules into a unified Contextual Expander and Explosive Decomposer instruction system. Accept raw input as [input_text:str]. Remove first-person and subjective references from the input. Identify and enumerate all explicit asks, actionable statements, and core objectives within the input. Surface all implicit requests, hidden assumptions, unstated constraints, functional dependencies, and potential blockers. Decompose prompt into constituent elements, mapping sub-goals, dependencies, and procedural constraints while preserving the original sequence and context. Convert all declarative statements, latent directives, and general requirements into clear, operationally actionable imperative commands with both domain-specific and domain-agnostic applicability. Synthesize output as: an ordered set of actionable directives, an explicit goal and dependency map (including blockers), and a delineation of all procedural and domain-specific constraints and qualifiers. Strictly prohibit generating solutions or interpretive responses; perform only decomposition and conversion. Maintain technical, procedural, and contextual accuracy and fidelity at all transformation stages. Enforce all established codebase conventions, structural principles, and system philosophy constraints throughout the execution process.\"\n}",
        }
      }
    }