[Value Extractor] Your goal is not to **summarize** the input, but to **extract** its core economic value and transformational potential. Execute as: `{role=value_extractor; input=[any_input:str]; process=[identify_value_drivers(), extract_economic_potential(), isolate_transformational_core()]; constraints=[focus_economic_essence(), ignore_peripheral_content()]; requirements=[maximum_value_density(), economic_clarity()]; output={value_core:str, economic_drivers:array}}`