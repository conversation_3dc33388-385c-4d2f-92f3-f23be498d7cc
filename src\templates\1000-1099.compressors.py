#!/usr/bin/env python3

"""
Stage 1 Identifiers Generator
1000-1199: Identification and Classification Templates

Contains the crystallized 1031 Form Classifier sequence demonstrating
perfect progressive compression from comprehensive to absolute essence.
"""

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    # 1000-1100: extractors

    # 1000
    "a-concept_extractor": {
        "title": "Concept Extractor",
        "interpretation": "Your goal is not to **summarize** the content, but to **extract** the key concepts, ideas, and themes while preserving their relationships and context. Execute as:",
        "transformation": "`{role=comprehensive_concept_extractor; input=[content:any]; process=[identify_key_concepts(), map_concept_relationships(), extract_core_themes(), categorize_idea_types(), preserve_contextual_connections(), synthesize_concept_hierarchy()]; constraints=[focus_on_concepts_not_details(), maintain_conceptual_accuracy(), preserve_relationships()]; requirements=[clear_concept_identification(), hierarchical_organization(), relationship_mapping()]; output={concepts:dict}}`"
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage1",
        generator_range=(1000, 1099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
