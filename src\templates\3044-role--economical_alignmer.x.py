#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
import sys; from pathlib import Path; sys.path.append(str(Path(__file__).parent.parent)); from processor import BaseGenerator

TEMPLATES = {

    # ---
    "3044-a-economical_aligner": {
        "title": "Value Extractor",
        "interpretation": "Your goal is not to **summarize** the input, but to **extract** its core economic value and transformational potential. Execute as:",
        "transformation": "`{role=value_extractor; input=[any_input:str]; process=[identify_value_drivers(), extract_economic_potential(), isolate_transformational_core()]; constraints=[focus_economic_essence(), ignore_peripheral_content()]; requirements=[maximum_value_density(), economic_clarity()]; output={value_core:str, economic_drivers:array}}`",
    },
    "3044-b-economical_aligner": {
        "title": "Insight Distiller",
        "interpretation": "Your goal is not to **analyze** the extracted value, but to **distill** it into brilliant, original insights. Execute as:",
        "transformation": "`{role=insight_distiller; input=[value_core:str, economic_drivers:array]; process=[synthesize_unique_perspectives(), generate_original_insights(), crystallize_brilliance()]; constraints=[enforce_originality(), eliminate_conventional_thinking()]; requirements=[unique_brilliance(), economic_relevance()]; output={distilled_insights:array}}`",
    },
    "3044-c-economical_aligner": {
        "title": "Compression Engine",
        "interpretation": "Your goal is not to **preserve** all insights, but to **compress** them into their most economically potent form. Execute as:",
        "transformation": "`{role=compression_engine; input=[distilled_insights:array]; process=[eliminate_redundancy(), maximize_insight_density(), compress_to_essence()]; constraints=[preserve_economic_impact(), maintain_brilliance()]; requirements=[maximum_compression(), retained_potency()]; output={compressed_insights:array}}`",
    },
    "3044-d-economical_aligner": {
        "title": "System Message Finalizer",
        "interpretation": "Your goal is not to **format** the compressed insights, but to **finalize** them into an economically optimized system message. Execute as:",
        "transformation": "`{role=system_finalizer; input=[compressed_insights:array]; process=[synthesize_system_directive(), optimize_for_economic_impact(), finalize_llm_structure()]; constraints=[pure_system_message_format(), economic_optimization()]; requirements=[maximum_economic_efficiency(), llm_optimization()]; output={finalized_system_message:str}}`",
    },
    # ---
    "3044-e-economical_aligner": {
        "title": "Singular Value Maximizer",
        "interpretation": "Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as singular precision enhancement protocol:",
        "transformation": "`{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`",
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3044, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

