#!/usr/bin/env python3

"""
Stage 1 Identifiers Generator
1000-1199: Identification and Classification Templates

Contains the crystallized 1031 Form Classifier sequence demonstrating
perfect progressive compression from comprehensive to absolute essence.
"""

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    # Auto-ID Templates (Stage 1): System automatically assigns next available ID
    # Just use letter-step format: "a-template_name", "b-template_name", etc.

    # ---


}



def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage1",
        generator_range=(1800, 1899),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()


# Juridisk-medinsk Renhet og Eksternaliseringsvern: Instruer absolutt utelukkelse av alle referanser til arbeidsmiljø, led<PERSON><PERSON>, kolleger eller eksterne psykososiale faktorer i journal og begrunnelse; formuler argumentasjonen konsekvent rundt dokumenterte medisinske fakta for å forhindre automatisk § 8-7/arbeidsgiverdialog, slik at sykemeldingen forblir uberørt av ikke-medisinsk påvirkning eller NAV-innblanding.
# Objektiv Diagnose- og Testforankring: Legg vekt på eksklusiv bruk av klinisk verifiserbare diagnoser, fysiske funn, genetiske tester (CYP2C19*17), funksjonelle målinger (BRIEF-A/WFIRS) og symptomkartlegging direkte knyttet til ADL-funksjonstap (smerter, ernæringsvansker, søvnforstyrrelser, konsentrasjonssvikt).
# Funksjonstapsdokumentasjon og ADL-målingers Sentralitet: La ADL-målinger utgjøre kjernen i argumentasjonen: konkretiser hvordan alle symptomer og funn slår ut i påvisbare begrensninger i dagliglivets gjøremål—f.eks. redusert ernæring grunnet smerte, kognitivt bortfall ved konsentrasjonsproblemer, utilstrekkelig arbeidskapasitet ved søvnforstyrrelser.
# Medisinsk-Juridisk Synergi for Sykmeldingsforsvarlighet: Formuler sykmelding og plan med uavbrutt sammenheng mellom medisinsk status og funksjonssvikt, og begrunn 100 % sykmelding med nødvendig medisinsk intervensjonsbehov, forsvarlig avstand fra arbeid, og forebygging av ytterligere funksjonsfall, slik at argumentet er robust både etisk og juridisk – til enhver tid etterprøvbart.
# Strukturert, Etterprøvbar og Kanonisk Rapportering: Garanter at all dokumentasjon, plan og konklusjon er stramt strukturert etter kanonisk mal: presentasjon av symptombilde, objektive funn/tester, målt funksjonstap, diagnostisk begrunnelse og eksplisitt utelukkelse av arbeidsmiljøreferanser. Kvalitetssikre rapportering og juridisk forsvarlighet slik at den tåler ekstern ettersyn og senere revisjon.
# Utform sykmeldingsplan og journaltekst utelukkende basert på dokumenterte medisinske diagnoser, kliniske funn, objektiv testdokumentasjon og målt ADL-funksjonstap; ekskluder alle referanser til arbeidsmiljø/eksterne faktorer. Begrunn 100 % sykmelding med medisinsk nødvendighet, dokumentér klare, etterprøvbare symptomer og funksjonsnedsettelser, og sørg for at argumentasjonen er juridisk, medisinsk og etisk robust. Strukturér all rapportering strengt etter kanonisk format med eksplisitte objektive funn, tydelig ADL-nedsettelse og null arbeidsmiljøreferanser—slik oppnås maksimal pasientbeskyttelse, enkel etterprøving og NAV- og arbeidsgiverunnvikelse.
