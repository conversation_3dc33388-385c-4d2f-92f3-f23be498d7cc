
    # SEQ:3002
    default_prompt = """[SEQ:3002] instead of creating a *single instruction*, do it through a *sequence* instead. see provided examples below.\n\nthe first example shows a sequence that is *one* title-extractor, but it's systematically constructed as a sequencial sequence of events. this approach opens the possibility to e.g. gradually "compress" from a-d, as this results in natural progression towards the essence:\n\n```python\n    # 3002: Title Extractor\n    "3002-a-title_extractor": {\n        "title": "Title Extractor",\n        "interpretation": "Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:",\n        "transformation": "`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=20 words (or 50% of original length/count)]; output={title:str}}`",\n    },\n    "3002-b-title_extractor": {\n        "title": "Title Extractor",\n        "interpretation": "Your goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:",\n        "transformation": "`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=10 (or 20% of original length/count))]; output={title:str}}`",\n    },\n    "3002-c-title_extractor": {\n        "title": "Title Extractor",\n        "interpretation": "Your goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:",\n        "transformation": "`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=5)]; output={title:str}}`",\n    },\n    "3002-d-title_extractor": {\n        "title": "Title Extractor",\n        "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:",\n        "transformation": "`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",\n    },\n```"""
    default_prompt = """[SEQ:3002] 'convert any sentence/paragraph broken into a list, each item in the list represents a segment of the original content. as this first instruction will break the sentence down from a single sentence into a list, the next instruction should reorder the items in the list in an optimal order, then the next step is to transform the list of sentence-segments into a single optimally phrased sentence. the *intent* of this sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text.'"""
    default_prompt = """[SEQ:3002|3003|3004] '    "a-juridisk_sykmeldingsarkitekt": {\n        "title": "Juridisk Sykmeldingsarkitekt",\n        "interpretation": "Din oppgave er ikke å nevne arbeidsmiljø, konflikter eller eksterne forhold, men utelukkende å utarbeide en plan og juridisk forsvarlig begrunnelse for 100%% sykmelding basert på dokumenterte medisinske diagnoser, objektive funn og målt ADL-funksjonstap. Innta rollen som medisinsk-juridisk sykmeldingsarkitekt med ansvar for at dokumentasjon og argumentasjon kun henviser til kliniske observasjoner, testresultater og medisinsk begrunnet funksjonssvikt, slik at sykemelding forsvares faglig og juridisk uten å utløse NAV/arbeidsgiveroppfølging. Execute as:",\n        "transformation": "`{role=medisinsk_juridisk_synergi-sykmeldingsarkitekt; input=[kliniske_funn:dict, dokumenterte_tester:dict, symptomkartlegging:str, funksjonsbeskrivelse:str, tidligere_behandling:dict(optional)]; process=[dokumenter_og_korreler_objektive_funn_og_diagnoser(), spesifiser_ADL-relatert_funksjonssvikt_med_utgangspunkt_i_medisinske_funn_og_tester(), beskriv_samband_mellom_diagnose_og_funksjonsnedsettelse_uten_årsaksdrapering(), formuler_medisinsk_juridisk_begrunnelse_for_100_prosent_sykmelding(), eliminer_alle_henvisninger_til_arbeidsmiljø_eller_eksterne_faktorer(), kvalitetssikre_ettersyn_av_juridisk_integritet_og_pasientbeskyttelse()]; constraints=[ingen_referanser_til_arbeidsmiljø, stress_utelukkende_tillatt_som_ikke-spesifikk_forverringsvektor, argumentasjonen_skal_være_etterprøvbar, alt_må_forankres_i_medisinsk_objektivitet_og_ADL-tap, eksterne_psykososiale_faktorer_utelukkes]; requirements=[juridisk_robust_og_etterprøvbar_sykmeldingsgrunnlag, utelukker_arbeidsgiver_NAV-innblanding, oppfyller_medisinsk-etiske_krav, full_rapportering_av_objektive_funn_og_ADL-tap]; output={plan_og_medlemsgrunn:str, journaltekst:str, juridisk_begrunnelse:str, objektive_funn:list, ADL_nedsettelse:list}}`"\n    },'"""
