├── stage1
│   ├── generators
│   │   ├── 1000-1099.compressors.py
│   │   ├── 1100-1199.rephrasers.py
│   │   ├── 1200-1299.generators.py
│   │   ├── 1300-1399.evaluators.py
│   │   ├── 1400-1499.translators.py
│   │   ├── 1500-1599.innovators.py
│   │   ├── 1600-1699.formalizers.py
│   │   ├── 1700-1799.expanders.py
│   │   ├── 1800-1899.transformers.py
│   │   └── 1900-1999.wip.py
│   └── README.md
├── stage2
│   ├── generators
│   │   ├── 2000-2099.compressors.py
│   │   ├── 2000-2099.todo.py
│   │   ├── 2700-2799.expanders.py
│   │   ├── 2800-2899.transformers.py
│   │   └── 2900-2999.special.py
│   └── README.md
├── stage3
│   ├── generators
│   │   ├── 3000-compressors.great--intent_distiller.3.history.py
│   │   ├── 3000-compressors.great--intent_distiller.3.py
│   │   ├── 3001-compressors.good--intent_distiller.6.history.py
│   │   ├── 3001-compressors.good--intent_distiller.6.py
│   │   ├── 3002-compressors.good--title_extractor.4.history.py
│   │   ├── 3002-compressors.good--title_extractor.4.py
│   │   ├── 3003-context--transformers.great--structural_reorder.4.py
│   │   ├── 3003-transformers.great--structural_reorder.4.history.py
│   │   ├── 3003-transformers.great--structural_reorder.4.py
│   │   ├── 3004-compressors.good--form_classifier.4.history.py
│   │   ├── 3004-compressors.good--form_classifier.4.py
│   │   ├── 3005-transformers--instruction_converter.4.history.py
│   │   ├── 3005-transformers--instruction_converter.4.py
│   │   ├── 3006-transformers--instruction_syntax_enforcer.5.py
│   │   ├── 3007-transformers--instruction_syntax_enforcer.5.py
│   │   ├── 3008-transformers--instruction_syntax_enforcer.6.py
│   │   ├── 3009-rephrasers--minimal_textual_nuance.6.py
│   │   ├── 3010-rephrasers--minimal_textual_nuance.6.py
│   │   ├── 3011-rephrasers.good--precision_refiner.6.history.py
│   │   ├── 3011-rephrasers.good--precision_refiner.6.py
│   │   ├── 3012-transformers.good--instruction_meta_generator.8.history.py
│   │   ├── 3012-transformers.good--instruction_meta_generator.8.py
│   │   ├── 3013-transformers.wip--incremental_text_harmonization.8.py
│   │   ├── 3014-rephrasers--structural_enhancer.x.py
│   │   ├── 3015-transformers.--prompt_generalizer.5.py
│   │   ├── 3016-enhancers.great--prompt_generalizer.x.py
│   │   ├── 3017-compressor.great--incremental_combiner.3.py
│   │   ├── 3018-compressor--universal_synthesis.5.py
│   │   ├── 3019-transformer--emplification_chain.5.py
│   │   ├── 3020-enhancers--wip.x.py
│   │   ├── 3021-rephrasers--leverage_amplifier.1.py
│   │   ├── 3022-critique.great--hard_critique.1.py
│   │   ├── 3023-direction--llm_optimizer.1.py
│   │   ├── 3024-enhancers--generalized_improver.5.py
│   │   ├── 3025-enhancers.great--value_amplifier.x.py
│   │   ├── 3026-enhancers.great--ultimate_amplifier.x.py
│   │   ├── 3027-enhancers.great--absolute_gain.x.py
│   │   ├── 3028-enhancers--absolute_gain.x.py
│   │   ├── 3029-enhancers--convergence_engine.x.py
│   │   ├── 3030-compressors.great--singular_value.1.py
│   │   ├── 3031-expanders--problem_exploder.1.py
│   │   ├── 3032-translator--english_norwegian_translator.1.py
│   │   ├── 3033-translator--norwegian_english_translator.1.py
│   │   ├── 3034-context--ringerikelandskap_seo.x.py
│   │   ├── 3035-context--seo_convergence.x.py
│   │   ├── 3036-enhancers.cheap--simple_value_maximizer.15.py
│   │   ├── 3037-rephrasers.great--rephrase_instruction.2.py
│   │   ├── 3038-compressors--intent_amplifier.5.py
│   │   ├── 3039-context--intent_amplifier.5.py
│   │   ├── 3040-context--intent_extractor.x.py
│   │   ├── 3041-context--intent_amplifier.5.py
│   │   ├── 3042-context--intent_amplifier.5.py
│   │   ├── 3043-context--expanders--problem_exploder.1.py
│   │   ├── 3044-role--economical_alignmer.x.py
│   │   ├── 3045-role--economical_alignmer.x.py
│   │   ├── 3046-enhancers--quote_synthesiser.x.py
│   │   ├── 3047-enhancers--quote_synthesiser.x.py
│   │   ├── 3048-enhancers--quote_synthesiser.x.py
│   │   ├── 3049-enhancers--quote_trajectory.x.py
│   │   ├── 3050-generators--witty_paradoxicalizer.x.py
│   │   ├── 3100-compressors.great--prompt_enhancer.6.history.py
│   │   ├── 3100-compressors.great--prompt_enhancer.6.py
│   │   ├── 3200-runway--prompt_architect.history.py
│   │   ├── 3200-runway--prompt_architect.py
│   │   └── 3900-compressors--wip.x.py
│   ├── md
│   │   ├── 3000-a-directional_critique.md
│   │   ├── 3000-b-directive_focuser.md
│   │   ├── 3000-c-intent_distiller.md
│   │   ├── 3003-a-structural_reorder.md
│   │   ├── 3003-b-structural_reorder.md
│   │   ├── 3003-c-structural_reorder.md
│   │   ├── 3003-d-structural_reorder.md
│   │   ├── 3022-a-hard_critique.md
│   │   ├── 3031-a-problem_exploder.md
│   │   ├── 3100-a-prompt_enhancer.md
│   │   ├── 3100-b-prompt_enhancer.md
│   │   └── 3100-c-prompt_enhancer.md
│   └── README.md
├── lvl1.md.templates.json
└── lvl1_md_to_json.py
