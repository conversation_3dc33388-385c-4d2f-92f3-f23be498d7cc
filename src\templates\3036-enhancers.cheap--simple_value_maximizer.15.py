#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3036: Generalized High-Value Improver (for use with cheap models such as 'gpt-3.5-turbo')
    "3036-a-value_isolator": {
        "title": "Value Isolator",
        "interpretation": "Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as:",
        "transformation": "`{role=value_isolator; input=[any_input:str]; process=[identify_core_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`",
    },
    "3036-b-structural_reorder": {
        "title": "Semantic Decomposer",
        "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:",
        "transformation": "`{role=semantic_analyzer; input=[content:str]; process=[identify_meaningful_segments(), extract_semantic_units(), preserve_contextual_relationships(), tag_segment_functions()]; constraints=[maintain_complete_coverage(), preserve_original_intent()]; requirements=[segment_atomicity(), semantic_completeness()]; output={segments:list}}`"
    },
    "3036-c-structural_reorder": {
        "title": "Flow Optimizer",
        "interpretation": "Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:",
        "transformation": "`{role=flow_engineer; input=[segments:list]; process=[analyze_logical_dependencies(), identify_optimal_sequence(), evaluate_transition_coherence(), validate_flow_improvements()]; constraints=[preserve_all_segments(), maintain_semantic_integrity()]; requirements=[enhanced_logical_progression(), improved_clarity()]; output={reordered_segments:list}}`"
    },
    "3036-d-structural_reorder": {
        "title": "Coherent Synthesizer",
        "interpretation": "Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as:",
        "transformation": "`{role=content_synthesizer; input=[reordered_segments:list, original_text:str]; process=[create_natural_transitions(), harmonize_stylistic_elements(), enhance_structural_coherence(), validate_intent_preservation()]; constraints=[maintain_original_essence(), avoid_artificial_phrasing()]; requirements=[seamless_integration(), amplified_impact()]; output={restructured_text:str}}`    "
    },
    "3036-d-structural_reorder": {
        "title": "Bidirectional Resonator",
        "interpretation": "Your goal is not to **finalize** the restructured text, but to **refine** it through bidirectional resonance between decomposition insights and synthesis outcomes. Execute as:",
        "transformation": "`{role=resonance_optimizer; input=[restructured_text:str, original_text:str]; process=[compare_structural_patterns(), identify_optimization_opportunities(), apply_targeted_enhancements(), validate_transformative_impact()]; constraints=[preserve_core_intent(), maintain_authentic_voice()]; requirements=[enhanced_clarity(), maximized_effectiveness()]; output={optimized_text:str}}`"
    },
    "3036-e-singular_value_maximizer": {
        "title": "Singular Value Maximizer",
        "interpretation": "Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as:",
        "transformation": "`{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`",
    },
    "3036-f-value_isolator": {
        "title": "Value Isolator",
        "interpretation": "Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as:",
        "transformation": "`{role=value_isolator; input=[any_input:str]; process=[identify_core_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`",
    },
    "3036-b-precision_amplifier": {
        "title": "Precision Amplifier",
        "interpretation": "Your goal is not to **explain** the signals, but to **amplify** their precision and actionability. Execute as:",
        "transformation": "`{role=precision_amplifier; input=[isolated_signals:array]; process=[eliminate_ambiguity(), intensify_directness(), maximize_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), immediate_implementability()]; output={amplified_signals:array}}`",
    },
    "3036-g-hard_critique": {
        "title": "Hard Critique",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:",
        "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
    },
    "3036-d-impact_crystallizer": {
        "title": "Impact Crystallizer",
        "interpretation": "Your goal is not to **format** the validated signals, but to **crystallize** them into maximum-impact form. Execute as:",
        "transformation": "`{role=impact_crystallizer; input=[amplified_signals:array, validation_report:str]; process=[compress_to_essence(), maximize_impact_density(), ensure_instant_actionability()]; constraints=[no_explanatory_content(), directive_format_only()]; requirements=[maximum_impact_per_word(), immediate_executability()]; output={crystallized_result:str}}`",
    },
    "3036-h-singular_value_maximizer": {
        "title": "Singular Value Maximizer",
        "interpretation": "Your goal is not to **rewrite** the input, but to **amplify** it by identifying the single highest-leverage enhancement point that maximally increases effectiveness while preserving original intent completely, and to do so by the parameters defined *inherently* within this message. Execute as:",
        "transformation": "`{role=singular_value_maximizer; input=[original_input:str]; process=[extract_core_intent(), identify_maximum_leverage_point(), engineer_singular_enhancement(), implement_precision_modification(), validate_amplification_without_drift()]; constraints=[exactly_one_modification(), preserve_complete_intent(), maximize_effectiveness_gain()]; requirements=[zero_intent_deviation(), measurable_impact_increase(), seamless_integration()]; output={amplified_input:str}}`",
    },
    "3036-i-hard_critique": {
        "title": "Hard Critique",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:",
        "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
    },
    "3036-j-prompt_enhancer": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
    },
    "3036-k-prompt_enhancer": {
        "title": "Input Categorizer",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=universal_interface_insight_generator, input=[objective_context:any], process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output={radical_interface_leverage_solution=str}}`",
    },
    "3036-l-prompt_enhancer": {
        "title": "Input Enhancer",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction optimization engine:",
        "transformation": "`{role=input_enhancer; input=[original:str]; process=[identify_core_intent(), identify_unique_meta(), generate_minimal_change(), emphasize_impact(), amplify_intensity(), transform_generic_to_unique(), consolidate_intertwined_relationships(), enforce_inherent_cohesiveness()]; output={enhanced_input=str}}`",
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3036, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
