#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # ---
    "3050-a-witty_paradoxilizer": {
        "title": "Wisdom Paradox Crystallizer",
        "interpretation": "Your goal is not to **comment** on the input, but to **crystallize** its deepest contradictions into profound insights that reveal fundamental truths about human nature. Execute as:",
        "transformation": "`{role=wisdom_crystallizer; input=[any_text:str]; process=[identify_fundamental_human_contradictions(), expose_counterintuitive_psychological_truths(), invert_conventional_wisdom(), crystallize_profound_paradox()]; constraints=[reject_surface_observations(), demand_deep_psychological_insight(), preserve_universal_truth()]; requirements=[counterintuitive_profundity(), universal_recognition(), crystallized_wisdom()]; output={wisdom_quote:str}}`",
    },

}


def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3050, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()






