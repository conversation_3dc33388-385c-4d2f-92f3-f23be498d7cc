#!/usr/bin/env python3
"""
Interactive CLI for AI Systems - Enhanced user experience
Reuses all existing components without modification to main.py
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime

# Import all existing components - zero changes needed to main.py
from main import (
    TemplateCatalog, SequenceManager, Config,
    ExecutorConfig, execute_sequence,
    PromptParser
)

class InteractiveCLI:
    """Enhanced interactive CLI that wraps existing functionality."""

    def __init__(self):
        self.catalog = None
        self.config_loaded = False
        self.recent_sequences = []

    async def run(self):
        """Main interactive loop."""
        print("🚀 AI Systems - Interactive Mode")
        print("=" * 50)
        print("Enhanced CLI for streamlined sequence building and execution")

        # Initialize (reuse existing setup)
        await self.initialize()

        while True:
            try:
                choice = await self.show_main_menu()
                if choice == 'quit':
                    break
                await self.handle_choice(choice)
            except KeyboardInterrupt:
                print("\n\nGoodbye! 👋")
                break

    async def initialize(self):
        """Initialize using existing components."""
        if not self.config_loaded:
            print("🔧 Initializing system...")
            Config.configure_litellm()
            self.catalog = TemplateCatalog.regenerate_catalog(force=False)
            self.config_loaded = True
            print("✅ System ready!")

    async def show_main_menu(self):
        """Display main menu and get user choice."""
        print("\n" + "=" * 50)
        print("📋 Main Menu")
        print("=" * 50)
        print("1. 🔍 Browse & Execute Sequences")
        print("2. ⚡ Quick Execute (with smart defaults)")
        print("3. 🛠️  Advanced Sequence Builder")
        print("4. 📊 View Available Models")
        print("5. 📁 Recent Executions")
        print("6. 🔧 System Information")
        print("q. 🚪 Quit")

        choice = input("\n👉 Select option: ").strip().lower()
        return choice

    async def handle_choice(self, choice):
        """Handle menu selection."""
        handlers = {
            '1': self.browse_sequences,
            '2': self.quick_execute,
            '3': self.advanced_builder,
            '4': self.view_models,
            '5': self.recent_executions,
            '6': self.system_info,
            'q': lambda: 'quit'
        }

        handler = handlers.get(choice)
        if handler:
            result = await handler()
            return result
        else:
            print("❌ Invalid choice. Please try again.")

    async def browse_sequences(self):
        """Browse available sequences interactively."""
        print("\n🔍 Sequence Browser")
        print("-" * 30)

        if not self.catalog:
            print("❌ No catalog loaded")
            return

        sequences = self.catalog.get("sequences", {})
        if not sequences:
            print("❌ No sequences found")
            return

        # Group by stage for better organization
        stages = {}
        for seq_id, seq_data in sequences.items():
            stage = self.get_stage_from_id(seq_id)
            if stage not in stages:
                stages[stage] = []
            stages[stage].append((seq_id, seq_data))

        # Display by stage
        for stage in sorted(stages.keys()):
            stage_name = self.get_stage_name(stage)
            print(f"\n📁 {stage_name}")
            for seq_id, seq_data in sorted(stages[stage]):
                step_count = len(seq_data) if isinstance(seq_data, list) else 1
                print(f"  {seq_id}: {step_count} steps")

        # Allow selection
        print("\n💡 Examples: '3000', '3000:a-c', '3000|3031', 'keyword:distill'")
        selected = input("Enter sequence specification (or 'back'): ").strip()

        if selected != 'back' and selected:
            prompt = input("Enter your prompt: ").strip()
            if prompt:
                models = self.select_models()
                await self.execute_with_params(prompt, selected, models)

    def get_stage_from_id(self, seq_id):
        """Extract stage number from sequence ID."""
        try:
            num = int(seq_id)
            if 1000 <= num <= 1999:
                return 1
            elif 2000 <= num <= 2999:
                return 2
            elif 3000 <= num <= 3999:
                return 3
            else:
                return 0
        except Exception:
            return 0

    def get_stage_name(self, stage):
        """Get descriptive stage name."""
        names = {
            1: "Stage 1: Prototyping & Testing (1000-1999)",
            2: "Stage 2: Validated & Unplaced (2000-2999)",
            3: "Stage 3: Production Ready (3000-3999)",
            0: "Other Sequences"
        }
        return names.get(stage, f"Stage {stage}")

    def select_models(self):
        """Interactive model selection."""
        print("\n🤖 Select Models:")
        print("1. gpt-4o (recommended)")
        print("2. claude-3-sonnet")
        print("3. Multiple models")
        print("4. Custom")

        choice = input("Choice (1-4, default=1): ").strip() or "1"

        if choice == "1":
            return ["gpt-4o"]
        elif choice == "2":
            return ["claude-3-sonnet"]
        elif choice == "3":
            return ["gpt-4o", "claude-3-sonnet"]
        elif choice == "4":
            models_str = input("Enter comma-separated models: ").strip()
            return [m.strip() for m in models_str.split(",") if m.strip()]
        else:
            return ["gpt-4o"]

    async def quick_execute(self):
        """Quick execution with smart defaults."""
        print("\n⚡ Quick Execute")
        print("-" * 20)
        print("💡 You can embed specs in your prompt:")
        print("   [SEQ:3000|3031] [MODEL:gpt-4o,claude-3-sonnet] Your prompt here")

        # Get prompt
        prompt = input("\nEnter your prompt: ").strip()
        if not prompt:
            print("❌ Prompt required")
            return

        # Check for embedded sequence/model specs
        cleaned_prompt, seq_spec, models_list = PromptParser.extract_all_from_prompt(prompt)

        # Use smart defaults if not embedded
        if not seq_spec:
            print("\n🎯 No sequence specified. Recommended sequences:")
            print("  3000 - Intent distiller (general purpose)")
            print("  3031 - Problem exploder (analysis)")
            print("  3100 - Prompt enhancer (improvement)")
            seq_spec = input("Enter sequence (default=3000): ").strip() or "3000"

        if not models_list:
            models_list = ["gpt-4o"]  # Smart default

        print(f"\n📝 Prompt: {cleaned_prompt}")
        print(f"🔗 Sequence: {seq_spec}")
        print(f"🤖 Models: {', '.join(models_list)}")

        confirm = input("\n✅ Execute? (Y/n): ").strip().lower()
        if confirm != 'n':
            await self.execute_with_params(cleaned_prompt, seq_spec, models_list)

    async def execute_with_params(self, prompt, sequence_spec, models):
        """Execute using existing execution engine."""
        try:
            print(f"\n🚀 Executing sequence '{sequence_spec}'...")

            # Resolve sequence using existing SequenceManager
            sequence_steps = SequenceManager.resolve_sequence_specification(
                self.catalog, sequence_spec
            )

            if not sequence_steps:
                print(f"❌ Sequence '{sequence_spec}' not found")
                return

            print(f"📋 Found {len(sequence_steps)} steps")

            # Create config using existing ExecutorConfig
            config = ExecutorConfig(
                sequence_steps=sequence_steps,
                user_prompt=prompt,
                sequence_id=sequence_spec,
                models=models,
                output_file=None,  # No file output in interactive mode
                system_instruction_extractor=TemplateCatalog.get_system_instruction,
                show_inputs=True,
                show_system_instructions=False,  # Less verbose for interactive
                show_responses=True,
                chain_mode=True
            )

            # Execute using existing execute_sequence function
            await execute_sequence(config)

            # Track for recent executions
            self.recent_sequences.append({
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'prompt': prompt[:50] + "..." if len(prompt) > 50 else prompt,
                'sequence': sequence_spec,
                'models': models
            })

            print("\n✅ Execution completed!")

        except Exception as e:
            print(f"❌ Execution failed: {e}")

    async def execute_single_sequence(self, seq_id):
        """Execute a specific sequence."""
        prompt = input(f"Enter prompt for sequence {seq_id}: ").strip()
        if prompt:
            models = ["gpt-4o"]  # Default model
            await self.execute_with_params(prompt, seq_id, models)

    async def advanced_builder(self):
        """Advanced sequence building."""
        print("\n🛠️ Advanced Sequence Builder")
        print("-" * 35)
        print("Build complex sequences with multiple steps and filters")
        print("\n📖 Syntax Guide:")
        print("  Single sequence:     3000")
        print("  Range filter:        3000:a-c")
        print("  Multiple sequences:  3000|3031|3100")
        print("  Keyword search:      keyword:distill")
        print("  Complex example:     3000:a-b|3031|keyword:enhance")

        sequence_spec = input("\nEnter sequence specification: ").strip()
        if not sequence_spec:
            return

        # Validate and preview
        try:
            sequence_steps = SequenceManager.resolve_sequence_specification(
                self.catalog, sequence_spec
            )

            if sequence_steps:
                print(f"\n✅ Valid sequence with {len(sequence_steps)} steps:")
                for i, (step_id, template) in enumerate(sequence_steps[:5], 1):
                    title = template.get('parts', {}).get('title', 'Unknown') if isinstance(template, dict) else 'Text step'
                    print(f"  {i}. Step {step_id}: {title}")
                if len(sequence_steps) > 5:
                    print(f"  ... and {len(sequence_steps) - 5} more steps")

                prompt = input("\nEnter prompt for this sequence: ").strip()
                if prompt:
                    models = self.select_models()
                    await self.execute_with_params(prompt, sequence_spec, models)
            else:
                print("❌ No steps found for this specification")

        except Exception as e:
            print(f"❌ Invalid sequence specification: {e}")

    async def view_models(self):
        """View available models with details."""
        print("\n📊 Available Models")
        print("-" * 25)

        models = Config.get_available_models()
        for provider, provider_models in models.items():
            print(f"\n🏢 {provider.title()}")
            for model in provider_models:
                default_marker = " ⭐ (default)" if model["is_default"] else ""
                print(f"  • {model['name']}{default_marker}")
                if model['name'] != model['model_id']:
                    print(f"    ID: {model['model_id']}")

        input("\nPress Enter to continue...")

    async def recent_executions(self):
        """Show recent executions."""
        print("\n📁 Recent Executions")
        print("-" * 25)

        if not self.recent_sequences:
            print("No recent executions found.")
            return

        for i, execution in enumerate(self.recent_sequences[-10:], 1):
            print(f"{i}. {execution['timestamp']}")
            print(f"   Prompt: {execution['prompt']}")
            print(f"   Sequence: {execution['sequence']}")
            print(f"   Models: {', '.join(execution['models'])}")
            print()

        input("Press Enter to continue...")

    async def system_info(self):
        """Display system information."""
        print("\n🔧 System Information")
        print("-" * 25)

        if self.catalog:
            meta = self.catalog.get('catalog_meta', {})
            print(f"📊 Catalog Level: {meta.get('level', 'Unknown')}")
            print(f"📅 Generated: {meta.get('generated_at', 'Unknown')}")
            print(f"📁 Total Templates: {meta.get('total_templates', 0)}")
            print(f"🔗 Total Sequences: {meta.get('total_sequences', 0)}")

            stages = meta.get('stage_distribution', {})
            for stage, info in stages.items():
                print(f"\n📂 {stage.title()}: {info.get('count', 0)} templates")
                print(f"   Range: {info.get('range', 'Unknown')}")
                print(f"   Description: {info.get('description', 'Unknown')}")
        else:
            print("❌ No catalog loaded")

        print(f"\n🐍 Python: {sys.version}")
        print(f"📁 Working Directory: {os.getcwd()}")

        input("\nPress Enter to continue...")


async def main():
    """Main entry point for interactive CLI."""
    cli = InteractiveCLI()
    await cli.run()


if __name__ == "__main__":
    asyncio.run(main())
